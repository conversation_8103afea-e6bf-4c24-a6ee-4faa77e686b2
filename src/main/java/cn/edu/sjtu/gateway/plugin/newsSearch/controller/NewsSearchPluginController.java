package cn.edu.sjtu.gateway.plugin.newsSearch.controller;

import cn.edu.sjtu.gateway.manager.entity.News;
import cn.edu.sjtu.gateway.manager.entity.SiteColumn;
import cn.edu.sjtu.gateway.plugin.newsSearch.vo.SearchResultVO;
import cn.edu.sjtu.gateway.vm.pluginManage.controller.BasePluginController;
import cn.edu.sjtu.gateway.vm.service.SqlService;
import cn.edu.sjtu.gateway.vm.util.Page;
import cn.edu.sjtu.gateway.vm.util.Sql;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 站内文章搜索
 * <AUTHOR>
 */
@Controller
@RequestMapping("/plugin/newsSearch/")
public class NewsSearchPluginController extends BasePluginController {
	private final SqlService sqlService;

	public NewsSearchPluginController(SqlService sqlService) {
		this.sqlService = sqlService;
	}

	/**
	 * 进行搜索，网站 ajax 方式请求,搜索当前网站中，栏目类型为新闻、图文列表下的信息
	 * @param siteid 站点编号，id
	 * @param title 要从标题中搜索的关键词
	 * @param everyPageNumber 每页显示多少条信息。取值范围限制 2 ～ 30 之间
	 */
	@RequestMapping("search${url.suffix}")
	@ResponseBody
	public SearchResultVO search(HttpServletRequest request,HttpServletResponse response,
			@RequestParam(value = "siteid", required = false , defaultValue="0") int siteid,
			@RequestParam(value = "title", required = false , defaultValue="") String title,
			@RequestParam(value = "everyPageNumber", required = false , defaultValue="10") int everyPageNumber){
		response.addHeader("Access-Control-Allow-Origin", "*");

		SearchResultVO vo = new SearchResultVO();
		if(siteid == 0){
			vo.setBaseVO(BaseVO.FAILURE, "请传入站点编号{site.id}");
			return vo;
		}
		if(title.isEmpty()){
			vo.setList(new ArrayList<>());
			vo.setPage(new Page(0, everyPageNumber, request));
			return vo;
		}
		if(everyPageNumber > 30 || everyPageNumber < 2){
			vo.setBaseVO(BaseVO.FAILURE, "everyPageNumber 每页显示多少条信息，范围必须在 2～30 之间");
			return vo;
		}

		Sql sql = new Sql(request);
		sql.setSearchTable("news");
		sql.appendWhere("siteid = "+siteid+" AND status = "+News.STATUS_NORMAL);
		sql.appendWhere("( type = "+News.TYPE_NEWS+" OR type = "+News.TYPE_IMAGENEWS+" OR type = "+SiteColumn.TYPE_LIST+")");
		// 修改这里，将title参数加入到搜索条件中，实现模糊搜索
		sql.setSearchColumn(new String[]{"title"});
		// 手动添加标题搜索条件以确保使用LIKE模糊搜索
		if (title != null && !title.isEmpty()) {
			sql.appendWhere("title LIKE '%" + title + "%'");
		}
		int count = sqlService.count("news", sql.getWhere());
		Page page = new Page(count, everyPageNumber, request);
		//创建查询语句，只有SELECT、FROM，原生sql查询。其他的where、limit等会自动拼接
		sql.setSelectFromAndPage("SELECT id,addtime,title,titlepic,intro,cid FROM news", page);
		//v4.4版本以前，没有自定义内容排序功能，只有按时间倒序排列
		sql.setDefaultOrderBy("addtime DESC");
		//因联合查询，结果集是没有实体类与其对应，故而用List<Map>接收
		List<Map<String, Object>> list = sqlService.findMapBySql(sql);

		vo.setList(list);
		vo.setPage(page);
		return vo;
	}

}