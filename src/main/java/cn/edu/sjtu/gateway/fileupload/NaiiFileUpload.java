package cn.edu.sjtu.gateway.fileupload;

import cn.edu.sjtu.gateway.fileupload.bean.NaiiSubFileBean;
import cn.edu.sjtu.gateway.fileupload.local.NaiiLocalStorage;
import cn.edu.sjtu.gateway.fileupload.vo.NaiiUploadFileVO;
import cn.edu.sjtu.gateway.vm.system.responseBody.WMResponseBody;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.tools.Lang;
import cn.edu.sjtu.gateway.tools.StringUtil;
import cn.edu.sjtu.gateway.tools.UrlUtil;
import cn.edu.sjtu.gateway.tools.media.ImageUtil;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;


/**
 * 文件上传，附件的操作，如云存储、或服务器本地文件
 * 如果是localFile ，则需要设置 AttachmentFile.netUrl
 *
 * <AUTHOR>
 */
public class NaiiFileUpload {
    public final static String UTF8 = "UTF-8";
    public final static String GBK = "GBK";
    /**
     * 默认的允许上传的文件后缀，如果没有使用 {@link #setAllowUploadSuffix(String)} 设置允许上传的后缀，那默认就是使用这里的。凡是这里的，都允许上传
     */
    public final static String DEFAULT_ALLOW_UPLOAD_SUFFIXS = "png|jpg|jpeg|gif|bmp|flv|swf|mkv|avi|rm|rmvb|mpeg|mpg|ogg|ogv|mov|wmv|mp4|webm|mp3|wav|mid|rar|zip|tar|gz|7z|bz2|cab|iso|doc|docx|xls|xlsx|ppt|pptx|pdf|txt|md|xml";
    private static final Logger log = LoggerFactory.getLogger(NaiiFileUpload.class);

    //允许上传的文件最大是多大，比如3MB 单位使 KB、MB
    private String maxFileSize;
    /**
     * -- SETTER --
     * 设置最大上传文件大小。不建议使用，请使用
     *
     * @param maxFileSizeKB 单位是KB，比如传入10，则代表最大上传文件大小为 10KB
     */
    //最大上传限制，单位：KB，在getMaxFileSizeKB()获取
    @Setter
    private int maxFileSizeKB = -1;

    /**
     * -- GETTER --
     * 获取当前可允许上传的后缀
     *
     * @return 返回可允许上传的后缀名，格式如 {"jpg","png","gif"}
     */
    //允许上传的后缀名数组，存储如 jpg 、 gif、zip
    @Getter
    public String[] allowUploadSuffixs;

    /**
     * -- SETTER --
     * 设置当前使用的存储模式。如果设置了此处，那么数据库中 ATTACHMENT_FILE_MODE 字段设置的存储方式将会失效，不会起任何作用。以此接口的设置为准
     *
     * @param storage 实现 {@link NaiiStorageInterface}接口
     */
    //实际执行的存储动作。不可直接使用，需使用 getStorage() 获取
    @Setter
    private NaiiStorageInterface storage;

    /**
     * -- SETTER --
     * 设置上传后文件所访问URL的域名
     * <p>
     * <p>
     * -- GETTER --
     * 获取上传后文件所访问URL的域名
     *
     * @param domain 当前正在使用的附件的域名，传入如： http://xxxx.com/  注意格式，后面以 / 结尾
     * @return 返回如 http://res.weiunity.com/   若找不到，则返回null
     */
    //文件URL访问域名，格式如 http://res.zvo.cn/ 注意格式使协议开头，/结尾。 例如上传了一个文件到 image/head.jpg ，那这个文件的URL为 netUrl+"image/head.jpg"
    @Getter
    @Setter
    public String domain = null;

    //默认的忽略限制，也就是文件大小、文件后缀名的限制。传入true，则是不再受这些限制条件判断，直接上传
    public boolean defaultIgnoreConstrands = false;

    /**
     * 获取当前使用的存储模式，进行存储。
     *
     * @return 如果在数据库表 system 表加载成功之前调用此方法，会返回null，当然，这个空指针几乎可忽略。实际使用中不会有这种情况
     */
    public NaiiStorageInterface getStorage() {
        if (this.storage == null) {
            //赋予默认本地存储模式
            this.storage = new NaiiLocalStorage();
            log.info("use default storage mode : local server");
        }
        return storage;
    }


    /**
     * 判断当前文件附件存储使用的是哪种存储方式
     *
     * @param storageClassName 存储方式的实现类的名字，如默认带的本地存储为  ，这里如果要判断是否是使用的本地存储，可传入 "NaiiLocalStorage"
     * @return 是否使用
     * <ul>
     * 	<li>true ： 是此种模式</li>
     * 	<li>false ： 不是此种模式</li>
     * </ul>
     */
    public boolean isStorage(String storageClassName) {
        if ("localFile".equalsIgnoreCase(storageClassName)) {
            storageClassName = "NaiiLocalStorage";
        }
        //取得当前实现的文件的名字，例如本地存储的命名为 LocalServerMode.java ,那这里会取到 LocalServerMode
        String currentModeFileName = this.getStorage().getClass().getSimpleName();
        return currentModeFileName.equalsIgnoreCase(storageClassName);
    }

    /**
     * 判断当前文件附件存储使用的是哪种存储方式
     *
     * @param storageClass 存储方式的实现类，如默认带的本地存储为 ，这里如果要判断是否是使用的本地存储，可传入 NaiiLocalStorage.class
     * @return 是否使用
     * <ul>
     * 	<li>true ： 是此种模式</li>
     * 	<li>false ： 不是此种模式</li>
     * </ul>
     */
    public boolean isStorage(Class storageClass) {
        String name = storageClass.getSimpleName();
        return isStorage(name);
    }


    /**
     * 获取当前允许上传的文件的最大大小
     *
     * @return 如 3MB 、 400KB 等
     */
    public String getMaxFileSize() {
        if (this.maxFileSize == null) {
            this.maxFileSize = "3MB";
        }
        return maxFileSize;
    }

    /**
     * 获取当前限制的上传文件最大的大小限制。单位是KB
     *
     * @return 单位KB
     */
    public long getMaxFileSizeKBback() {
        if (maxFileSizeKB == -1) {
            //未初始化，那么进行初始化
            maxFileSize = getMaxFileSize();

            if (maxFileSize.indexOf("KB") > 0) {
                //使用KB单位
                maxFileSizeKB = Lang.stringToInt(maxFileSize.replace("KB", "").trim(), 0);
                if (maxFileSizeKB == 0) {
                    log.error("maxFileSize 异常,当前获取到的为0");
                }
            } else if (maxFileSize.indexOf("MB") > 0) {
                //使用MB
                maxFileSizeKB = Lang.stringToInt(maxFileSize.replace("MB", "").trim(), 0) * 1024;
                if (maxFileSizeKB == 0) {
                    log.error("maxFileSize 异常,当前获取到的为0");
                }
            } else if (maxFileSize.indexOf("GB") > 0) {
                //使用 GB
                maxFileSizeKB = Lang.stringToInt(maxFileSize.replace("GB", "").trim(), 0) * 1024 * 1024;
                if (maxFileSizeKB == 0) {
                    log.error("maxFileSize 异常,当前获取到的为0");
                }
            } else {
                //没有找到合适单位，报错
                log.error("maxFileSize exception, not find unit，your are KB ? MB ? GB ? Please use one of them");
            }
        }
        return maxFileSizeKB;
    }

    public long getMaxFileSizeKB() {
        if (maxFileSizeKB == -1) {
            // 未初始化，进行初始化
            maxFileSize = getMaxFileSize();

            String unit = "";
            int multiplier = 1;

            if (maxFileSize.endsWith("KB")) {
                unit = "KB";
                // KB 不需要转换
            } else if (maxFileSize.endsWith("MB")) {
                unit = "MB";
                multiplier = 1024; // MB 转换成 KB
            } else if (maxFileSize.endsWith("GB")) {
                unit = "GB";
                multiplier = 1024 * 1024; // GB 转换成 KB
            }

            if (!unit.isEmpty()) {
                String numericPart = maxFileSize.replace(unit, "").trim();
                maxFileSizeKB = Lang.stringToInt(numericPart, 0) * multiplier;

                if (maxFileSizeKB == 0) {
                    log.error("maxFileSize 异常, 当前值为 0");
                }
            } else {
                // 未识别的单位
                log.error("maxFileSize 异常: 未找到合适单位 (请使用 KB, MB, 或 GB)");
            }
        }

        return maxFileSizeKB;
    }

    /**
     * 判断要上传的文件是否超出大小限制，若超出大小限制，返回出错原因
     *
     * @param file 要上传的文件，判断其大小是否超过系统指定的最大限制
     * @return 若超出大小，则返回result:Failure ，info为出错原因
     */
    public BaseVO verifyFileMaxLength(File file) {
        BaseVO vo = new BaseVO();
        if (file != null) {
            //文件的KB长度
            int lengthKB = (int) Math.ceil((double) file.length() / 1024);
            vo = verifyFileMaxLength(lengthKB);
        }
        return vo;
    }

    /**
     * 判断要上传的文件是否超出大小限制，若超出大小限制，返回出错原因
     *
     * @param lengthKB 要上传的文件的大小，判断其大小是否超过系统指定的最大限制，单位是KB (1024B=1KB)
     * @return 若超出大小，则返回result:Failure ，info为出错原因
     */
    public BaseVO verifyFileMaxLength(long lengthKB) {
        BaseVO vo = new BaseVO();
        if (getMaxFileSizeKB() > 0 && lengthKB > getMaxFileSizeKB()) {
            vo.setBaseVO(BaseVO.FAILURE, "文件大小超出限制！上传大小在 " + maxFileSize + " 以内");
            return vo;
        }
        return vo;
    }


    /**
     * 设置允许上传的文件最大是多大，比如3MB 单位为 KB、MB
     *
     * @param maxSize 传入入  3MB  单位有 KB、MB
     */
    public void setMaxFileSize(String maxSize) {
        if (maxSize == null) {
            return;
        }
        this.maxFileSize = maxSize;
        this.maxFileSizeKB = -1;    //清空转化，使用时重新将 maxFileSize 进行转化
    }


    /**
     * 设置允许可上传的后缀名。
     *
     * @param allowUploadSuffixs 传入格式入 png|jpg|gif|zip 多个用英文|分割
     */
    public void setAllowUploadSuffix(String allowUploadSuffixs) {
        if (allowUploadSuffixs == null) {
            return;
        }

        String ss[] = allowUploadSuffixs.split("\\|");
        //过滤一遍，空跟无效的
        List<String> list = new ArrayList<String>();
        for (String string : ss) {
            String s = string.trim();
            if (!s.isEmpty()) {
                list.add(s);
            }
        }

        //初始化创建数组
        this.allowUploadSuffixs = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            this.allowUploadSuffixs[i] = list.get(i);
        }
    }


    /**
     * 设置允许可上传的后缀名。
     *
     * @param allowUploadSuffixs 传入格式如 {"jpg","png","gif"}
     */
    public void setAllowUploadSuffixs(String[] allowUploadSuffixs) {
        if (allowUploadSuffixs == null) {
            return;
        }
        this.allowUploadSuffixs = allowUploadSuffixs;
    }


    /**
     * 判断当前后缀名是否在可允许上传的后缀中
     *
     * @param path 上传文件要保存到的路径。传入如 site/219/index.html 当然，你也可以直接传入具体后缀，如 html
     * @return true：可上传，允许上传，后缀在指定的后缀列表中
     */
    public boolean isAllowUpload(String path) {
        if (path == null || path.trim().isEmpty()) {
            return false; // 如果路径为空，不允许上传
        }

        String suffix;
        if (path.contains(".")) {
            //发现路径，需要取后缀
            suffix = Lang.findFileSuffix(path);
        } else {
            //未发现路径，传入的就是后缀
            suffix = path;
        }

        if (suffix == null || suffix.trim().isEmpty()) {
            return false; // 如果后缀为空，不允许上传
        }

        //判断是否设置允许上传什么后缀
        if (allowUploadSuffixs == null || allowUploadSuffixs.length == 0) {
            //还未设置，那默认使用 DEFAULT_ALLOW_UPLOAD_SUFFIXS
            setAllowUploadSuffix(DEFAULT_ALLOW_UPLOAD_SUFFIXS);
        }

        //进行判断，判断传入的suffix是否在允许上传的后缀里面
        return WMResponseBody.isIgnoreField(suffix, allowUploadSuffixs);
    }
    /**
     * 上传本地文件
     *
     * @param path      上传后的文件所在的目录、路径，如 "jar/file/"
     * @param localPath 本地要上传的文件的绝对路径，如 "/jar_file/iw.jar"
     * @return 若失败，返回null
     */
    public NaiiUploadFileVO upload(String path, String localPath) {
        return upload(path, localPath, this.defaultIgnoreConstrands);
    }

    /**
     * 上传本地文件
     *
     * @param path             上传后的文件所在的目录、路径，如 "jar/file/"
     * @param localPath        本地要上传的文件的绝对路径，如 "/jar_file/iw.jar"
     * @param ignoreConstrands 忽略限制，也就是文件大小、文件后缀名的限制。传入true，则是不再受这些限制条件判断，直接上传
     * @return 若失败，返回null
     */
    public NaiiUploadFileVO upload(String path, String localPath, boolean ignoreConstrands) {
        File localFile = new File(localPath);
        return upload(path, localFile, ignoreConstrands);
    }

    /**
     * 上传本地文件。上传的文件名会被自动重命名为uuid+后缀
     *
     * @param path      上传后的文件所在的目录、路径，如 "jar/file/"
     * @param localFile 本地要上传的文件
     * @return 若失败，返回null
     */
    public NaiiUploadFileVO upload(String path, File localFile) {
        return upload(path, localFile, this.defaultIgnoreConstrands);
    }


    /**
     * 上传本地文件。上传的文件名会被自动重命名为uuid+后缀
     *
     * @param path             上传后的文件所在的目录、路径，如 "jar/file/"
     * @param localFile        本地要上传的文件
     * @param ignoreConstrands 忽略限制，也就是文件大小、文件后缀名的限制。传入true，则是不再受这些限制条件判断，直接上传
     * @return 若失败，返回null
     */
    public NaiiUploadFileVO upload(String path, File localFile, boolean ignoreConstrands) {
        NaiiUploadFileVO vo = new NaiiUploadFileVO();

        BaseVO baseVO = verifyFileMaxLength(localFile);
        if (baseVO.getResult() - BaseVO.FAILURE == 0) {
            vo.setBaseVO(baseVO);
            return vo;
        }

        //将本地文件转化为流，使用try-with-resources确保流被正确关闭
        try (InputStream localInput = new FileInputStream(localFile)) {
            return upload(path, localInput, ignoreConstrands);
        } catch (FileNotFoundException e) {
            log.error("上传出错，要上传的文件不存在！", e);
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "上传出错，要上传的文件不存在！");
            return vo;
        } catch (IOException e) {
            log.error("上传过程中发生IO异常: {}", e.getMessage(), e);
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "文件上传失败: " + e.getMessage());
            return vo;
        }
    }

    /**
     * 上传文件。上传后的文件名固定
     *
     * @param path        上传到哪里，包含上传后的文件名，如"image/head/123.jpg"
     *                    <p>注意，这里是跟着上传的文件名的，文件名叫什么，就保存成什么</p>
     * @param inputStream 文件
     */
    public NaiiUploadFileVO upload(String path, InputStream inputStream) {
        return upload(path, inputStream, this.defaultIgnoreConstrands);
    }


    /**
     * 上传文件。上传后的文件名固定
     *
     * @param path             上传到哪里，包含上传后的文件名，如"image/head/123.jpg"
     *                         <p>注意，这里是跟着上传的文件名的，文件名叫什么，就保存成什么</p>
     * @param inputStream      文件
     * @param ignoreConstrands 忽略限制，也就是文件大小、文件后缀名的限制。传入true，则是不再受这些限制，直接上传
     */
    public NaiiUploadFileVO upload(String path, InputStream inputStream, boolean ignoreConstrands) {
        NaiiUploadFileVO vo = new NaiiUploadFileVO();

        /*** 判断上传的文件是否有选择 ***/
        if (inputStream == null) {
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "请选择要上传的文件");
            return vo;
        }

        //获取文件大小
        long length_B = 0;
        try {
            length_B = inputStream.available();
            vo.setSize(length_B);
            log.info("上传文件大小: {} 字节, 路径: {}", length_B, path);
        } catch (IOException e) {
            log.warn("无法获取文件大小: {}", e.getMessage());
        }

        if (!ignoreConstrands) {
            /** 判断存储出去的后缀是否合规 **/
            if (!isAllowUpload(path)) {  // 修正逻辑，使用 !isAllowUpload
                vo.setBaseVO(NaiiUploadFileVO.FAILURE, "该后缀不允许被上传");
                return vo;
            }

            /** 判断文件大小是否超出最大限制的大小 **/
            BaseVO baseVO = verifyFileMaxLength((long) Math.ceil((double) length_B / 1024));
            if (baseVO.getResult() - BaseVO.FAILURE == 0) {
                vo.setBaseVO(baseVO);
                return vo;
            }
        }

        // 使用缓冲的输入流来提高性能
        try (BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream)) {
            //执行上传
            vo = getStorage().upload(path, bufferedInputStream);
            if (vo.getSize() < 1) {
                vo.setSize(length_B);
            }

            //设置网络下载地址
            String domain = getDomain();
            if (domain != null) {
                vo.setUrl(domain + vo.getPath());
            }
            //提取文件名
            vo.setName(UrlUtil.getFileName("http://zvo.cn/" + path));

            log.info("文件上传成功: {}, 大小: {} 字节", path, vo.getSize());
        } catch (IOException e) {
            log.error("文件上传过程中发生IO异常: {}", e.getMessage(), e);
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "文件上传失败: " + e.getMessage());
        } catch (OutOfMemoryError e) {
            log.error("文件上传过程中内存不足: {}", e.getMessage(), e);
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "服务器内存不足，请稍后再试或联系管理员");
            System.gc(); // 请求垃圾回收
        }

        return vo;
    }


    /**
     * 上传文件
     *
     * @param path        上传后的文件所在的目录、路径，如 "jar/file/"
     * @param inputStream 要上传的文件的数据流
     * @param fileSuffix  上传的文件的后缀名
     */
    public NaiiUploadFileVO upload(String path, InputStream inputStream, String fileSuffix) {
        return upload(path, inputStream, fileSuffix, this.defaultIgnoreConstrands);
    }

    /**
     * 上传文件
     *
     * @param path             上传后的文件所在的目录、路径，如 "jar/file/"
     * @param inputStream      要上传的文件的数据流
     * @param fileSuffix       上传的文件的后缀名
     * @param ignoreConstrands 忽略限制，也就是文件大小、文件后缀名的限制。传入true，则是不再受这些限制条件判断，直接上传
     */
    public NaiiUploadFileVO upload(String path, InputStream inputStream, String fileSuffix, boolean ignoreConstrands) {
        NaiiUploadFileVO vo = new NaiiUploadFileVO();

        if (!ignoreConstrands && !isAllowUpload(fileSuffix)) {
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "此后缀名不在可上传文件列表中");
            log.warn("文件上传失败，不允许的后缀名: {}", fileSuffix);
            return vo;
        }

        if (inputStream == null) {
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "上传文件不存在，请选择要上传的文件");
            return vo;
        }

        return upload(path, "." + fileSuffix, inputStream, ignoreConstrands);
    }


    /**
     * 上传文件
     *
     * @param path        上传后的文件所在的目录、路径，如 "jar/file/"
     * @param fileName    上传的文件名，如“xnx3.jar”；主要拿里面的后缀名。也可以直接传入文件的后缀名如“.jar。新的文件名会是自动生成的 uuid+后缀”
     * @param inputStream {@link java.io.InputStream}
     */
    public NaiiUploadFileVO upload(String path, String fileName, InputStream inputStream) {
        return upload(path, fileName, inputStream, this.defaultIgnoreConstrands);
    }


    /**
     * 上传文件
     *
     * @param path             上传后的文件所在的目录、路径，如 "jar/file/"
     * @param fileName         上传的文件名，如“xnx3.jar”；主要拿里面的后缀名。也可以直接传入文件的后缀名如“.jar。新的文件名会是自动生成的 uuid+后缀”
     * @param inputStream      {@link java.io.InputStream}
     * @param ignoreConstrands 忽略限制，也就是文件大小、文件后缀名的限制。传入true，则是不再受这些限制条件判断，直接上传
     * @return 若失败，返回null
     */
    public NaiiUploadFileVO upload(String path, String fileName, InputStream inputStream, boolean ignoreConstrands) {
        NaiiUploadFileVO vo = new NaiiUploadFileVO();

        //进行文件后缀校验
        if (fileName == null || !fileName.contains(".")) {
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "上传的文件名(后缀)校验失败！传入的为：" + fileName + "，允许传入的值如：a.jpg或.jpg");
            return vo;
        }

        String fileSuffix = StringUtil.subString(fileName, ".", null, 3);    //获得文件后缀，以便重命名
        String name = Lang.uuid() + "." + fileSuffix;
        return upload(path + name, inputStream, ignoreConstrands);
    }


    /**
     * 给出文本内容，写出文件
     *
     * @param path   写出的路径,上传后的文件所在的目录＋文件名，如 "jar/file/xnx3.html"
     * @param text   文本内容
     * @param encode 编码格式，可传入
     */
    public NaiiUploadFileVO uploadString(String path, String text, String encode) {
        return uploadString(path, text, encode, this.defaultIgnoreConstrands);
    }


    /**
     * 给出文本内容，写出文件
     *
     * @param path             写出的路径,上传后的文件所在的目录＋文件名，如 "jar/file/xnx3.html"
     * @param text             文本内容
     * @param encode           编码格式，可传入
     * @param ignoreConstrands 忽略限制，也就是文件大小、文件后缀名的限制。传入true，则是不再受这些限制条件判断，直接上传
     */
    public NaiiUploadFileVO uploadString(String path, String text, String encode, boolean ignoreConstrands) {
        try (InputStream inputStream = StringUtil.stringToInputStream(text, encode)) {
            return upload(path, inputStream, ignoreConstrands);
        } catch (UnsupportedEncodingException e) {
            log.error("字符串编码转换失败: {}", e.getMessage(), e);
            NaiiUploadFileVO vo = new NaiiUploadFileVO();
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "字符串编码转换失败: " + e.getMessage());
            return vo;
        } catch (IOException e) {
            log.error("字符串上传过程中发生IO异常: {}", e.getMessage(), e);
            NaiiUploadFileVO vo = new NaiiUploadFileVO();
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "字符串上传失败: " + e.getMessage());
            return vo;
        }
    }

    /**
     * 给出文本内容，写出文件。写出UTF－8编码
     *
     * @param path 写出的路径,上传后的文件所在的目录＋文件名，如 "jar/file/xnx3.html"
     * @param text 文本内容
     */
    public NaiiUploadFileVO uploadString(String path, String text) {
        return uploadString(path, text, "UTF-8", this.defaultIgnoreConstrands);
    }


    /**
     * 给出文本内容，写出文件。写出UTF－8编码
     *
     * @param path             写出的路径,上传后的文件所在的目录＋文件名，如 "jar/file/xnx3.html"
     * @param text             文本内容
     * @param ignoreConstrands 忽略限制，也就是文件大小、文件后缀名的限制。传入true，则是不再受这些限制条件判断，直接上传
     */
    public NaiiUploadFileVO uploadString(String path, String text, boolean ignoreConstrands) {
        return uploadString(path, text, "UTF-8", ignoreConstrands);
    }


    /**
     * 上传图片文件
     *
     * @param path        上传后的文件所在的目录、路径，如 "jar/file/"
     * @param inputStream 图片的数据流
     * @param fileSuffix  图片的后缀名
     * @param maxWidth    上传图片的最大宽度，若超过这个宽度，会对图片进行等比缩放为当前宽度
     */
    public NaiiUploadFileVO uploadImage(String path, InputStream inputStream, String fileSuffix, int maxWidth) {
        return uploadImage(path, inputStream, fileSuffix, maxWidth, this.defaultIgnoreConstrands);
    }


    /**
     * 上传图片文件
     *
     * @param path             上传后的文件所在的目录、路径，如 "jar/file/"
     * @param inputStream      图片的数据流
     * @param fileSuffix       图片的后缀名
     * @param maxWidth         上传图片的最大宽度，若超过这个宽度，会对图片进行等比缩放为当前宽度
     * @param ignoreConstrands 忽略限制，也就是文件大小、文件后缀名的限制。传入true，则是不再受这些限制条件判断，直接上传
     */
    public NaiiUploadFileVO uploadImage(String path, InputStream inputStream, String fileSuffix, int maxWidth, boolean ignoreConstrands) {
        NaiiUploadFileVO vo = new NaiiUploadFileVO();

        if (!ignoreConstrands && !isAllowUpload(fileSuffix)) {
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "此后缀名不在可上传文件列表中");
            log.warn("图片上传失败，不允许的后缀名: {}", fileSuffix);
            return vo;
        }

        if (inputStream == null) {
            vo.setBaseVO(NaiiUploadFileVO.FAILURE, "请选择要上传的文件");
            return vo;
        }

        //判断其是否进行图像压缩
        if (maxWidth > 0) {
            inputStream = ImageUtil.proportionZoom(inputStream, maxWidth, fileSuffix);
        }

        return upload(path, "." + fileSuffix, inputStream, ignoreConstrands);
    }

    /**
     * 上传图片，将网上的图片复制到自己这里。（如果网上图片的URL获取不到后缀，默认用 jpg）
     *
     * @param path     上传后的文件所在的目录、路径。 传入格式如： file/images/  会自动给上传的图片重命名保存
     * @param imageUrl 网上图片的地址
     */
    public NaiiUploadFileVO uploadImage(String path, String imageUrl) {
        return uploadImage(path, imageUrl, this.defaultIgnoreConstrands);
    }

    /**
     * 上传图片，将网上的图片复制到自己这里。（如果网上图片的URL获取不到后缀，默认用 jpg）
     *
     * @param path             上传后的文件所在的目录、路径。 传入格式如： file/images/  会自动给上传的图片重命名保存
     * @param imageUrl         网上图片的地址
     * @param ignoreConstrands 忽略限制，也就是文件大小、文件后缀名的限制。传入true，则是不再受这些限制条件判断，直接上传
     */
    public NaiiUploadFileVO uploadImage(String path, String imageUrl, boolean ignoreConstrands) {
        if (imageUrl == null) {
            return null;
        }
        String suffix = Lang.findFileSuffix(imageUrl);    //取图片后缀名
        BufferedImage bufferedImage = ImageUtil.getBufferedImageByUrl(imageUrl);
        if (suffix == null) {
            suffix = "jpg";
        }

        return upload(path + Lang.uuid() + "." + suffix, ImageUtil.bufferedImageToInputStream(bufferedImage, suffix), ignoreConstrands);
    }

    /**
     * 传入一个路径，得到其源代码(文本)
     *
     * @param path 要获取的文本内容的路径，如  site/123/index.html
     * @return 返回其文本内容。若找不到，或出错，则返回 null
     */
    public String getText(String path) {
        InputStream is = getStorage().get(path);
        if (is == null) {
            return null;
        }

        try (InputStream inputStream = is) {
            return StringUtil.inputStreamToString(inputStream, "UTF-8");
        } catch (IOException e) {
            log.debug("读取文件内容失败: {}", e.getMessage());
            return null;
        }
    }


    /**
     * 传入一个路径，取得文件数据
     *
     * @param path 要获取的文件的路径，如  site/123/index.html
     * @return 返回文件数据。若找不到，或出错，则返回 null
     */
    public InputStream getInputStream(String path) {
        InputStream is = getStorage().get(path);
        return is;
    }

    /**
     * 删除文件
     *
     * @param path 文件所在的路径，如 "jar/file/xnx3.jpg"
     */
    public void delete(String path) {
        getStorage().delete(path);
    }

    /**
     * 复制文件
     *
     * @param originalFilePath 原本文件所在的路径(相对路径，非绝对路径，操作的是当前附件文件目录下)
     * @param newFilePath      复制的文件所在的路径，所放的路径。(相对路径，非绝对路径，操作的是当前附件文件目录下)
     */
    public void copy(String originalFilePath, String newFilePath) {
        getStorage().copyFile(originalFilePath, newFilePath);
    }


    /**
     * 获取某个目录（文件夹）占用空间的大小
     *
     * @param path 要计算的目录(文件夹)，如 jar/file/
     * @return 计算出来的大小。单位：字节，B。  千分之一KB
     */
    public long getDirectorySize(String path) {
        return getStorage().getSize(path);
    }

    /**
     * 获取某个文件的大小，这个是文件，如果传入文件夹，是不起作用的，会返回-1，文件未发现
     *
     * @param path 要获取的是哪个文件。传入如 site/219/1.html
     * @return 单位是 B， * 1000 = KB 。 如果返回-1，则是文件未发现，文件不存在
     */
    public long getFileSize(String path) {
        return getStorage().getSize(path);
    }

    /**
     * 获取某个目录下的子文件列表。获取的只是目录下第一级的子文件，并非是在这个目录下无论目录深度是多少都列出来
     *
     * @param path 要获取的是哪个目录的子文件。传入如 site/219/
     * @return 该目录下一级子文件（如果有文件夹，也包含文件夹）列表。如果size为0，则是没有子文件或文件夹。无论什么情况不会反null
     */
    public List<NaiiSubFileBean> getSubFileList(String path) {
        return getStorage().getSubFileList(path);
    }

    /**
     * 创建文件夹
     *
     * @param path 要创建的文件路径，传入如 site/219/test/ 则是创建 test 文件夹
     */
    public void createFolder(String path) {
        getStorage().createFolder(path);
    }
}
