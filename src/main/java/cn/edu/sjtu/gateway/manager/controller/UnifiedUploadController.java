package cn.edu.sjtu.gateway.manager.controller;

import cn.edu.sjtu.gateway.fileupload.vo.NaiiUploadFileVO;
import cn.edu.sjtu.gateway.manager.service.SiteService;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.service.SqlService;
import cn.edu.sjtu.gateway.vm.service.UserService;
import cn.edu.sjtu.gateway.vm.util.AttachmentUtil;
import cn.edu.sjtu.gateway.vm.util.SessionUtil;
import cn.edu.sjtu.gateway.vm.util.file.FileUploadUtil;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.vm.vo.UploadFileVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 统一文件上传控制器
 * 整合所有文件上传功能，提供统一的接口
 * <AUTHOR>
 */
@Controller
@RequestMapping("/upload")
@Slf4j
public class UnifiedUploadController extends BaseController {
    
    // 上传默认限制
    private static final String DEFAULT_IMAGE_EXTS = ".jpg,.jpeg,.png,.gif";
    private static final String DEFAULT_FILE_EXTS = ".jpg,.jpeg,.png,.gif,.bmp,.flv,.swf,.mkv,.avi,.rm,.rmvb,.mpeg,.mpg,.ogg,.ogv,.mov,.wmv,.mp4,.webm,.mp3,.wav,.mid,.rar,.zip,.tar,.gz,.7z,.bz2,.cab,.iso,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt,.md,.xml";
    private static final String DEFAULT_MAX_SIZE = "100MB";

    private final SqlService sqlService;
    private final SiteService siteService;
    private final UserService userService;

    public UnifiedUploadController(SqlService sqlService, SiteService siteService, UserService userService) {
        this.sqlService = sqlService;
        this.siteService = siteService;
        this.userService = userService;
    }

    /**
     * 通用文件上传接口
     * 
     * @param file 要上传的文件
     * @param type 上传类型(image/file)
     * @param size 最大大小限制
     * @param exts 允许的文件扩展名
     * @return 上传结果
     */
    @RequestMapping(value = "file${url.suffix}", method = RequestMethod.POST)
    @ResponseBody
    public NaiiUploadFileVO uploadFile(@RequestParam("file") MultipartFile file,
                                       @RequestParam(value = "type", required = false, defaultValue = "file") String type,
                                       @RequestParam(value = "size", required = false, defaultValue = DEFAULT_MAX_SIZE) String size,
                                       @RequestParam(value = "exts", required = false) String exts) {
        try {
            log.info("接收到文件上传请求: 文件名={}, 大小={}KB, 类型={}", 
                    file.getOriginalFilename(), file.getSize()/1024, type);
            
            return processFileUpload(file, type, size, exts);
        } catch (Exception e) {
            log.error("文件上传过程中发生错误: ", e);
            return createFailureResponse("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 富文本编辑器图片上传接口 (兼容TinyMCE)
     * 
     * @param file 要上传的文件
     * @return 上传结果
     */
    @RequestMapping(value = "tinymce${url.suffix}", method = RequestMethod.POST)
    @ResponseBody
    public NaiiUploadFileVO uploadForTinyMCE(@RequestParam("file") MultipartFile file) {
        try {
            log.info("接收到TinyMCE图片上传请求: 文件名={}, 大小={}KB", 
                    file.getOriginalFilename(), file.getSize()/1024);
            
            return processFileUpload(file, "image", DEFAULT_MAX_SIZE, DEFAULT_IMAGE_EXTS);
        } catch (Exception e) {
            log.error("TinyMCE图片上传过程中发生错误: ", e);
            return createFailureResponse("图片上传失败: " + e.getMessage());
        }
    }

    /**
     * Markdown编辑器图片上传接口
     * 
     * @param file 要上传的文件
     * @param request HTTP请求
     * @return 上传结果
     */
    @RequestMapping(value = "markdown${url.suffix}", method = RequestMethod.POST)
    @ResponseBody
    public UploadFileVO uploadForMarkdown(@RequestParam("editormd-image-file") MultipartFile file,
                                          HttpServletRequest request) {
        try {
            log.info("接收到Markdown图片上传请求: 文件名={}, 大小={}KB", 
                    file.getOriginalFilename(), file.getSize()/1024);
            
            UploadFileVO uploadFileVO = AttachmentUtil.uploadImage(
                    "site/" + getSiteId() + "/news/", request, "editormd-image-file", 0);
            
            if (uploadFileVO.getResult() == UploadFileVO.SUCCESS) {
                log.info("Markdown图片上传成功: 文件路径 - {}", uploadFileVO.getPath());
            } else {
                log.error("Markdown图片上传失败: {}", uploadFileVO.getInfo());
            }
            
            return uploadFileVO;
        } catch (Exception e) {
            log.error("Markdown图片上传过程中发生错误: ", e);
            UploadFileVO errorVO = new UploadFileVO();
            errorVO.setResult(BaseVO.FAILURE);
            errorVO.setInfo("图片上传失败: " + e.getMessage());
            return errorVO;
        }
    }

    /**
     * 处理文件上传的核心方法
     * 
     * @param file 要上传的文件
     * @param type 上传类型(image/file)
     * @param size 最大大小限制
     * @param exts 允许的文件扩展名
     * @return 上传结果
     */
    private NaiiUploadFileVO processFileUpload(MultipartFile file, String type, String size, String exts) {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            return createFailureResponse("文件不存在或为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || fileName.trim().isEmpty()) {
            return createFailureResponse("文件名不能为空");
        }

        // 获取文件扩展名
        String fileExtension = getFileExtension(fileName);
        log.info("处理文件上传: {} (类型: {}), 大小: {}KB", fileName, fileExtension, file.getSize()/1024);

        // 确定允许的扩展名
        String allowedExts = exts;
        if (allowedExts == null || allowedExts.isEmpty()) {
            allowedExts = "image".equalsIgnoreCase(type) ? DEFAULT_IMAGE_EXTS : DEFAULT_FILE_EXTS;
        }

        // 校验文件扩展名
        if (!isExtensionAllowed(fileExtension, allowedExts)) {
            log.warn("文件类型不允许: {} (允许的类型: {})", fileExtension, allowedExts);
            return createFailureResponse("不允许的文件类型: " + fileExtension);
        }

        try {
            // 配置上传工具的限制
            if (allowedExts != null && !allowedExts.isEmpty()) {
                String allowedSuffixes = allowedExts.replaceAll("\\.", "").replaceAll(",", "|");
                FileUploadUtil.fileupload.setAllowUploadSuffix(allowedSuffixes);
                log.info("设置允许的文件类型: {}", allowedSuffixes);
            }

            if (size != null && !size.isEmpty()) {
                FileUploadUtil.fileupload.setMaxFileSize(size);
                log.info("设置最大文件大小: {}", size);
            }

            // 上传文件
            String uploadPath = "site/" + super.getSiteId() + "/news/";
            log.info("开始上传文件到路径: {}", uploadPath);

            UploadFileVO uploadFileVO;
            if ("image".equalsIgnoreCase(type)) {
                // 图片上传
                uploadFileVO = AttachmentUtil.uploadImageByMultipartFile(uploadPath, file);
            } else {
                // 普通文件上传
                uploadFileVO = AttachmentUtil.uploadFile(uploadPath, file);
            }

            if (uploadFileVO.getResult() == UploadFileVO.SUCCESS) {
                log.info("上传成功: 文件路径 - {}, 大小: {}", uploadFileVO.getPath(), file.getSize());
                
                // 转换为NaiiUploadFileVO
                NaiiUploadFileVO naiiUploadFileVO = new NaiiUploadFileVO();
                naiiUploadFileVO.setName(uploadFileVO.getFileName());
                naiiUploadFileVO.setPath(uploadFileVO.getPath());
                naiiUploadFileVO.setUrl(uploadFileVO.getUrl());
                naiiUploadFileVO.setSize(uploadFileVO.getSize());
                naiiUploadFileVO.setBaseVO(BaseVO.SUCCESS, "上传成功");
                return naiiUploadFileVO;
            } else {
                log.error("文件上传失败: {}", uploadFileVO.getInfo());
                return createFailureResponse(uploadFileVO.getInfo());
            }
        } catch (Exception e) {
            log.error("处理文件上传时发生异常: {}", e.getMessage(), e);
            return createFailureResponse("文件上传处理失败: " + e.getMessage());
        }
    }


    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 文件扩展名
     */
    private String getFileExtension(String fileName) {
        return Objects.requireNonNull(fileName).substring(fileName.lastIndexOf(".")).toLowerCase();
    }

    /**
     * 检查文件扩展名是否被允许
     * 
     * @param fileExtension 文件扩展名
     * @param allowedExts 允许的扩展名列表
     * @return 是否被允许
     */
    private boolean isExtensionAllowed(String fileExtension, String allowedExts) {
        if (fileExtension == null || fileExtension.isEmpty()) {
            return false;
        }
        
        // 确保扩展名以点开头
        if (!fileExtension.startsWith(".")) {
            fileExtension = "." + fileExtension;
        }
        
        return allowedExts.toLowerCase().contains(fileExtension.toLowerCase());
    }

    /**
     * 创建失败响应
     * 
     * @param message 错误消息
     * @return 失败响应对象
     */
    private NaiiUploadFileVO createFailureResponse(String message) {
        NaiiUploadFileVO uploadFileVO = new NaiiUploadFileVO();
        uploadFileVO.setResult(BaseVO.FAILURE);
        uploadFileVO.setInfo(message);
        log.info(message);
        return uploadFileVO;
    }

}