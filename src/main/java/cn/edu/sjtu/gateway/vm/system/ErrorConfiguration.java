package cn.edu.sjtu.gateway.vm.system;

import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.web.ErrorProperties;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver;
import org.springframework.boot.web.error.ErrorAttributeOptions;
import org.springframework.boot.web.servlet.error.ErrorAttributes;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 重写500错误，变为返回http 200状态码，返回 {"result": 0, "info": "处理异常，请稍后再试"}
 */
@Configuration
@Slf4j
public class ErrorConfiguration {

    @Bean
    public ErrorPageController basicErrorController(ErrorAttributes errorAttributes,
                                                    ServerProperties serverProperties,
                                                    ObjectProvider<List<ErrorViewResolver>> errorViewResolversProvider) {
        return new ErrorPageController(errorAttributes, serverProperties.getError(),
                errorViewResolversProvider.getIfAvailable());
    }
}

@Slf4j
class ErrorPageController extends BasicErrorController {

    public ErrorPageController(ErrorAttributes errorAttributes, ErrorProperties errorProperties,
                               List<ErrorViewResolver> errorViewResolvers) {
        super(errorAttributes, errorProperties, errorViewResolvers);
    }

    @Override
    protected Map<String, Object> getErrorAttributes(HttpServletRequest request, ErrorAttributeOptions options) {
        Map<String, Object> errorAttributes = super.getErrorAttributes(request, options);
        logErrorDetails(request, errorAttributes);

        // 设置返回内容
        errorAttributes.put("result", BaseVO.FAILURE);
        errorAttributes.put("info", "处理异常，请稍后再试");

        String status = String.valueOf(errorAttributes.getOrDefault("status", ""));
        if ("400".equals(status)) {
            errorAttributes.put("info", "传入参数异常，请检查是否有必填项未传入，或传入的某个参数的类型不符");
        }

        // 删除不必要的字段
        List.of("trace", "timestamp", "path", "status").forEach(errorAttributes.keySet()::remove);

        return errorAttributes;
    }

    private void logErrorDetails(HttpServletRequest request, Map<String, Object> errorAttributes) {
        StringBuilder logBuilder = new StringBuilder("-------- ERROR START -------\n");

        errorAttributes.forEach((key, value) -> {
            if (value != null) {
                logBuilder.append(key).append(" : ").append(value).append("\n");
            }
        });

        logBuilder.append("Method : ").append(request.getMethod()).append("\n");
        logBuilder.append("Referer : ").append(request.getHeader("referer")).append("\n");

        if (!request.getParameterMap().isEmpty()) {
            logBuilder.append("Request Params:\n");
            request.getParameterMap().forEach((key, values) -> {
                String valueString = String.join(", ", values);
                if (valueString.length() > 100000) {
                    valueString = "... The content is too long, exceeding 100000 characters, omitted";
                }
                logBuilder.append("\t").append(key).append(" : ").append(valueString).append("\n");
            });
        }

        logBuilder.append("-------- ERROR END -------");
        log.error(logBuilder.toString());
    }

    @Override
    protected HttpStatus getStatus(HttpServletRequest request) {
        HttpStatus status = super.getStatus(request);
        return status.is5xxServerError() ? HttpStatus.OK : status;
    }
}
