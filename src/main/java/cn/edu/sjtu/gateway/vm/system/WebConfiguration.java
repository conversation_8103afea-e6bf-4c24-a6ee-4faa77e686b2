package cn.edu.sjtu.gateway.vm.system;

import cn.edu.sjtu.gateway.vm.Global;
import cn.edu.sjtu.gateway.vm.pluginManage.interfaces.manage.SpringMVCInterceptorPluginManage;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.AuthorizationException;
import org.springframework.boot.web.server.ErrorPage;
import org.springframework.boot.web.server.ErrorPageRegistrar;
import org.springframework.boot.web.server.ErrorPageRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ViewResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.view.InternalResourceViewResolver;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 整合的Web配置类，包括JSP配置、静态资源配置、拦截器配置、错误页面配置等
 * 替代原有的多个分散配置类，避免功能重复
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class WebConfiguration implements WebMvcConfigurer, ErrorPageRegistrar {

    static {
        // 启动时自动检测运行模式
        detectRunMode();
    }

    /**
     * 检测应用运行模式（JAR包或WAR包）
     */
    private static void detectRunMode() {
        try {
            String classPath = WebConfiguration.class.getProtectionDomain().getCodeSource().getLocation().toString();
            log.info("检测到的类路径: {}", classPath);

            if (classPath.contains(".jar!") || classPath.endsWith(".jar")) {
                // JAR包运行模式
                Global.isJarRun = true;
                log.info("检测到JAR包运行模式");
            } else if (classPath.contains("/target/classes/") || classPath.contains("/build/classes/")) {
                // 开发模式（IDE中运行）
                Global.isJarRun = false;
                log.info("检测到开发模式（IDE中运行）");
            } else if (classPath.contains("/WEB-INF/")) {
                // WAR包运行模式（Tomcat等）
                Global.isJarRun = false;
                log.info("检测到WAR包运行模式（Tomcat等）");
            } else {
                // 默认为JAR包模式
                Global.isJarRun = true;
                log.warn("无法确定运行模式，默认使用JAR包模式");
            }

            log.info("最终运行模式: {}", Global.isJarRun ? "JAR包模式" : "WAR包/开发模式");
        } catch (Exception e) {
            log.error("检测运行模式失败，使用默认JAR包模式", e);
            Global.isJarRun = true;
        }
    }

    /**
     * 注册jsp视图解析器
     *
     * @return
     * @description: 以viewResolver命名。阻止ContentNegotiatingViewResolver注入I0C容器，可当做jsp视图解析器
     * 为什么要阻止ContentNegotiatingViewResolver注入呢，因为这个视图解析器的默认order特别小，总放在集合最前面，它会选择最优视图解折器
     */
    @Bean
    public ViewResolver viewResolver() {
        InternalResourceViewResolver resolver = new InternalResourceViewResolver();

        // 根据运行模式设置不同的前缀路径
        if (SystemUtil.isJarRun()) {
            // JAR包运行时，JSP文件位于META-INF/resources下
            resolver.setPrefix("/WEB-INF/view/");
            log.info("JAR模式运行，JSP视图解析器前缀设置为: /WEB-INF/view/");
        } else {
            // WAR包或开发模式运行时
            resolver.setPrefix("/WEB-INF/view/");
            log.info("WAR模式运行，JSP视图解析器前缀设置为: /WEB-INF/view/");
        }

        resolver.setSuffix(".jsp");
        //当控制器返回的viewName符合规则时才使用这个视图解析器 如["jsp/*",""]，是个数组（controller 返回的时候可以写成jsp/test）返回的真实视图名就是 prefix +"jsp/test.jsp"
        //设置优先级,数值越小优先级越高
        resolver.setOrder(1);

        // 设置视图类，确保JSP能正确渲染
        resolver.setViewClass(org.springframework.web.servlet.view.JstlView.class);

        log.info("JSP视图解析器配置完成 - prefix: /WEB-INF/view/, suffix: .jsp, order: {}",
                resolver.getOrder());

        return resolver;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        for (int i = 0; i < SpringMVCInterceptorPluginManage.handlerInterceptorList.size(); i++) {
            Map<String, Object> map = SpringMVCInterceptorPluginManage.handlerInterceptorList.get(i);
            HandlerInterceptor handler = (HandlerInterceptor) map.get("class");
            List<String> pathPatterns = (List<String>) map.get("pathPatterns");
            registry.addInterceptor(handler).addPathPatterns(pathPatterns).order(i + 5);
        }
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        log.info("开始配置静态资源映射，当前运行模式: {}", Global.isJarRun ? "JAR包模式" : "WAR包/开发模式");

        // 添加通用静态资源映射
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600);

        registry.addResourceHandler("/plugin/**")
                .addResourceLocations("classpath:/static/plugin/")
                .setCachePeriod(3600);

        // 添加module路径的静态资源映射
        registry.addResourceHandler("/module/**")
                .addResourceLocations("classpath:/static/module/")
                .setCachePeriod(3600);

        // 添加直接访问css、js路径的映射（兼容旧版本引用方式）
        registry.addResourceHandler("/css/**")
                .addResourceLocations("classpath:/static/css/")
                .setCachePeriod(3600);

        registry.addResourceHandler("/js/**")
                .addResourceLocations("classpath:/static/js/")
                .setCachePeriod(3600);

        if (SystemUtil.isJarRun()) {
            //如果是以jar方式运行，则要虚拟路径
            String jarDirPath = SystemUtil.getJarDirPath();
            log.info("JAR包模式 - 上传文件基础路径: {}", jarDirPath);

            // 映射上传的文件资源 (修复路径映射)
            registry.addResourceHandler("/site/**")
                    .addResourceLocations("file:" + jarDirPath + "uploads/site/")
                    .setCachePeriod(3600);
            log.info("映射 /site/** -> file:{}", jarDirPath + "uploads/site/");

            // 映射缓存资源
            registry.addResourceHandler("/cache/**")
                    .addResourceLocations("file:" + jarDirPath + "cache/")
                    .setCachePeriod(3600);

            // 映射naii资源
            registry.addResourceHandler("/naii/**")
                    .addResourceLocations("file:" + jarDirPath + "cache/naii/")
                    .setCachePeriod(3600);

            // 映射上传的文件 - 这是最重要的映射
            registry.addResourceHandler("/uploads/**")
                    .addResourceLocations("file:" + jarDirPath + "uploads/")
                    .setCachePeriod(3600);
            log.info("映射 /uploads/** -> file:{}", jarDirPath + "uploads/");

            registry.addResourceHandler("/plugin_data/**")
                    .addResourceLocations("classpath:/plugin_data/");
            registry.addResourceHandler("/head/**")
                    .addResourceLocations("classpath:/head/");
            registry.addResourceHandler("/websiteTemplate/**")
                    .addResourceLocations("classpath:/websiteTemplate/");

            // JAR包模式下，添加临时目录的静态资源映射
            addTempStaticResourceMapping(registry);

            log.info("jar包方式运行，配置虚拟路径 /site、/cache、/head、/static、/plugin 和临时静态资源");
            log.info("JAR包所在目录: {}", jarDirPath);
        } else {
            // WAR包或开发模式下的静态资源配置
            String projectPath = SystemUtil.getProjectPath();
            log.info("WAR包/开发模式 - 上传文件基础路径: {}", projectPath);

            // 映射上传的文件资源 (修复路径映射)
            registry.addResourceHandler("/site/**")
                    .addResourceLocations("file:" + projectPath + "uploads/site/")
                    .setCachePeriod(3600);
            log.info("映射 /site/** -> file:{}", projectPath + "uploads/site/");

            // 映射缓存资源
            registry.addResourceHandler("/cache/**")
                    .addResourceLocations("file:" + projectPath + "cache/")
                    .setCachePeriod(3600);

            // 映射naii资源
            registry.addResourceHandler("/naii/**")
                    .addResourceLocations("file:" + projectPath + "cache/naii/")
                    .setCachePeriod(3600);

            // 映射上传的文件 - 这是最重要的映射
            registry.addResourceHandler("/uploads/**")
                    .addResourceLocations("file:" + projectPath + "uploads/")
                    .setCachePeriod(3600);
            log.info("映射 /uploads/** -> file:{}", projectPath + "uploads/");

            registry.addResourceHandler("/plugin_data/**")
                    .addResourceLocations("classpath:/plugin_data/");
            registry.addResourceHandler("/head/**")
                    .addResourceLocations("classpath:/head/");
            registry.addResourceHandler("/websiteTemplate/**")
                    .addResourceLocations("classpath:/websiteTemplate/");

            log.info("WAR模式运行，配置静态资源路径 /static、/plugin、/module、/css、/js");
        }
    }

    /**
     * 添加临时目录的静态资源映射（JAR包模式）
     */
    private void addTempStaticResourceMapping(ResourceHandlerRegistry registry) {
        try {
            String tempDir = System.getProperty("java.io.tmpdir");
            File[] jspDirs = new File(tempDir).listFiles((dir, name) -> name.startsWith("naii-jsp"));

            if (jspDirs != null && jspDirs.length > 0) {
                for (File jspDir : jspDirs) {
                    File staticDir = new File(jspDir, "static");
                    if (staticDir.exists()) {
                        String staticPath = "file:" + staticDir.getAbsolutePath().replace("\\", "/") + "/";

                        // 添加临时静态资源映射，优先级更高
                        registry.addResourceHandler("/static/**")
                                .addResourceLocations(staticPath)
                                .setCachePeriod(0); // 临时资源不缓存

                        registry.addResourceHandler("/plugin/**")
                                .addResourceLocations(staticPath + "plugin/")
                                .setCachePeriod(0);

                        registry.addResourceHandler("/module/**")
                                .addResourceLocations(staticPath + "module/")
                                .setCachePeriod(0);

                        registry.addResourceHandler("/css/**")
                                .addResourceLocations(staticPath + "css/")
                                .setCachePeriod(0);

                        registry.addResourceHandler("/js/**")
                                .addResourceLocations(staticPath + "js/")
                                .setCachePeriod(0);

                        log.info("添加临时静态资源映射: {}", staticPath);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("添加临时静态资源映射失败", e);
        }
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController("/").setViewName("forward:/index.html");
        registry.setOrder(Ordered.HIGHEST_PRECEDENCE);
    }

    /**
     * 错误页配置，如404、500错误
     */
    @Override
    public void registerErrorPages(ErrorPageRegistry registry) {
        ErrorPage[] errorPages = new ErrorPage[2];
        errorPages[0] = new ErrorPage(HttpStatus.NOT_FOUND, "/404.naii");
        //优先根据此来进行排查。 先根据具体异常的类、再根据错误码
        errorPages[1] = new ErrorPage(AuthorizationException.class, "/403.naii");

        registry.addErrorPages(errorPages);
    }

}