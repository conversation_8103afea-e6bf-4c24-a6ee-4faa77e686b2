package cn.edu.sjtu.gateway.vm.util;

import cn.edu.sjtu.gateway.fileupload.NaiiFileUpload;
import cn.edu.sjtu.gateway.fileupload.NaiiStorageInterface;
import cn.edu.sjtu.gateway.fileupload.vo.NaiiUploadFileVO;
import cn.edu.sjtu.gateway.vm.Global;
import cn.edu.sjtu.gateway.vm.util.AttachmentMode.ApplicationConfig;
import cn.edu.sjtu.gateway.vm.util.file.FileUploadUtil;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.vm.vo.UploadFileVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.InputStream;

/**
 * 文件上传，附件的操作，如云存储、或服务器本地文件
 * 如果是localFile ，则需要设置 AttachmentFile.netUrl。
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AttachmentUtil {

    //服务器本身磁盘进行附件存储
    public static final String MODE_LOCAL_FILE = "localFile";

    /**
     * 使用时，你不需要 new AttachmentUtil() ，你直接使用 AttachmentUtil. 即可！这个是交给tomcat启动后自动执行初始化用的
     */
    public AttachmentUtil() {

        new Thread(() -> {
            while (Global.system.isEmpty()) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    log.error("错误信息：------>" + e);
                }
            }
            //当 system数据表 加载后才会执行
            String mode = SystemUtil.get("ATTACHMENT_FILE_MODE");
            if (mode == null || mode.isEmpty()) {
                return;
            }
            if (mode.equalsIgnoreCase(MODE_LOCAL_FILE)) {
                //本地存储方式，检查配置文件中的domain配置是否已设置
                //如果配置文件中没有设置domain，则使用数据库中的配置（保持向后兼容）
                if (FileUploadUtil.fileupload.getDomain() == null && 
                    SystemUtil.get("ATTACHMENT_FILE_URL") != null && 
                    SystemUtil.get("ATTACHMENT_FILE_URL").length() > 3) {
                    
                    ApplicationConfig config = new ApplicationConfig();
                    config.setDomain(SystemUtil.get("ATTACHMENT_FILE_URL"));
                    FileUploadUtil.loadConfig(config);
                    log.debug("FileUpload 设置 system 表的 ATTACHMENT_FILE_URL ：{}", SystemUtil.get("ATTACHMENT_FILE_URL"));
                }
            }
        }).start();
    }


    /**
     * 获取当前使用的存储模式，进行存储。
     *
     * @return 如果在数据库表 system 表加载成功之前调用此方法，会返回null，当然，这个空指针几乎可忽略。实际使用中不会有这种情况
     */
    public static NaiiStorageInterface getStorageMode() {
        return FileUploadUtil.fileupload.getStorage();
    }

    /**
     * 获取当前允许上传的文件的最大大小
     *
     * @return 如 3MB 、 400KB 等
     */
    public static String getMaxFileSize() {
        return FileUploadUtil.fileupload.getMaxFileSize();
    }

    /**
     * 获取当前限制的上传文件最大的大小限制。单位是KB
     *
     * @return 单位KB
     */
    public static int getMaxFileSizeKB() {
        return (int) FileUploadUtil.fileupload.getMaxFileSizeKB();
    }

    /**
     * 判断当前文件附件存储使用的是哪种模式，存储到什么位置
     *
     * @param mode 存储的代码，可直接传入如
     * @return 是否使用
     * <ul>
     * 	<li>true ： 是此种模式</li>
     * 	<li>false ： 不是此种模式</li>
     * </ul>
     */
    public static boolean isMode(String mode) {
        return FileUploadUtil.fileupload.isStorage(mode);
    }


    /**
     * 获取附件访问的url地址
     */
    public static String netUrl() {
        if (FileUploadUtil.fileupload == null) {
            log.error("异常，文件存储组件 FileUploadUtil.fileupload 为 null，应该是安装或配置有误导致的，可到相关仓库中提issues");
            return "";
        }
        String domain = FileUploadUtil.fileupload.getDomain();
        if (domain == null) {
            domain = "/";
            FileUploadUtil.fileupload.setDomain("/");
            log.error("异常，文件存储组件 FileUploadUtil.fileupload.getDomain(); 为 null，应该是安装或配置有误导致的，已自动赋予 /");
        }
        return domain;
    }

    /**
     * 设置当前的netUrl
     *
     * @param url 当前正在使用的附件url前缀，传入如：
     */
    public static void setNetUrl(String url) {
        FileUploadUtil.fileupload.setDomain(url);
    }

    /**
     * 给出文本内容，写出文件
     *
     * @param filePath 写出的路径,上传后的文件所在的目录＋文件名，如 "jar/file/xnx3.html"
     * @param text     文本内容
     * @param encode   编码格式，可传入 {@link cn.edu.sjtu.fileupload.FileUpload#GBK}、{@link cn.edu.sjtu.fileupload.FileUpload#UTF8}
     */
    public static void uploadStringFile(String filePath, String text, String encode) {
        FileUploadUtil.fileupload.uploadString(filePath, text, encode);
    }

    /**
     * 给出文本内容，写出文件。写出UTF－8编码
     *
     * @param filePath 写出的路径,上传后的文件所在的目录＋文件名，如 "jar/file/xnx3.html"
     * @param text     文本内容
     */
    public static void uploadStringFile(String filePath, String text) {
        FileUploadUtil.fileupload.uploadString(filePath, text, NaiiFileUpload.UTF8);
    }

    /**
     * 判断要上传的文件是否超出大小限制，若超出大小限制，返回出错原因
     *
     * @param file 要上传的文件，判断其大小是否超过系统指定的最大限制
     * @return 若超出大小，则返回result:Failure ，info为出错原因
     */
    public static UploadFileVO verifyFileMaxLength(File file) {
        BaseVO vo = FileUploadUtil.fileupload.verifyFileMaxLength(file);
        UploadFileVO uploadFileVO = new UploadFileVO();
        uploadFileVO.setResult(vo.getResult());
        uploadFileVO.setInfo(vo.getInfo());
        return uploadFileVO;
    }

    /**
     * 判断要上传的文件是否超出大小限制，若超出大小限制，返回出错原因
     *
     * @param lengthKB 要上传的文件的大小，判断其大小是否超过系统指定的最大限制，单位是KB
     * @return 若超出大小，则返回result:Failure ，info为出错原因
     */
    public static UploadFileVO verifyFileMaxLength(int lengthKB) {
        BaseVO vo = FileUploadUtil.fileupload.verifyFileMaxLength(lengthKB);
        UploadFileVO uploadFileVO = new UploadFileVO();
        uploadFileVO.setResult(vo.getResult());
        uploadFileVO.setInfo(vo.getInfo());
        return uploadFileVO;
    }

    /**
     * 上传本地文件
     *
     * @param filePath  上传后的文件所在的目录、路径，如 "jar/file/"
     * @param localPath 本地要上传的文件的绝对路径，如 "/jar_file/iw.jar"
     * @return 若失败，返回null
     */
    public static UploadFileVO uploadFile(String filePath, String localPath) {
        return uploadFileVOTransfer(FileUploadUtil.fileupload.upload(filePath, localPath));
    }

    /**
     * 上传本地文件。上传的文件名会被自动重命名为uuid+后缀
     *
     * @param filePath  上传后的文件所在的目录、路径，如 "jar/file/"
     * @param localFile 本地要上传的文件
     * @return 若失败，返回null
     */
    public static UploadFileVO uploadFile(String filePath, File localFile) {
        return uploadFileVOTransfer(FileUploadUtil.fileupload.upload(filePath, localFile));
    }
    /**
     * 上传文件。上传后的文件名固定
     *
     * @param path        上传到哪里，包含上传后的文件名，如"image/head/123.jpg"
     *                    <p>注意，这里是跟着上传的文件名的，文件名叫什么，就保存成什么</p>
     * @param inputStream 文件
     * @return {@link cn.edu.sjtu.gateway.vm.vo.UploadFileVO}
     */
    public static UploadFileVO uploadFile(String path, InputStream inputStream) {
        return uploadFileVOTransfer(FileUploadUtil.fileupload.upload(path, inputStream));
    }


    /**
     * 上传图片，将网上的图片复制到自己这里。（如果网上图片的URL获取不到后缀，默认用 jpg）
     *
     * @param filePath 上传后的文件所在的目录、路径。 传入格式如： file/images/  会自动给上传的图片重命名保存
     * @param imageUrl 网上图片的地址
     * @return {@link cn.edu.sjtu.gateway.vm.vo.UploadFileVO}
     */
    public static UploadFileVO uploadImageByUrl(String filePath, String imageUrl) {
        return uploadFileVOTransfer(FileUploadUtil.fileupload.uploadImage(filePath, imageUrl));
    }

    /**
     * 传入一个路径，得到其源代码(文本)
     *
     * @param path 要获取的文本内容的路径，如  site/123/index.html
     * @return 返回其文本内容。若找不到，或出错，则返回 null
     */
    public static String getTextByPath(String path) {
        return FileUploadUtil.fileupload.getText(path);
    }

    /**
     * 删除文件
     *
     * @param filePath 文件所在的路径，如 "jar/file/xnx3.jpg"
     */
    public static void deleteObject(String filePath) {
        FileUploadUtil.fileupload.delete(filePath);
    }

    /**
     * 复制文件
     *
     * @param originalFilePath 原本文件所在的路径(相对路径，非绝对路径，操作的是当前附件文件目录下)
     * @param newFilePath      复制的文件所在的路径，所放的路径。(相对路径，非绝对路径，操作的是当前附件文件目录下)
     */
    public static void copyObject(String originalFilePath, String newFilePath) {
        FileUploadUtil.fileupload.copy(originalFilePath, newFilePath);
    }

    /**
     * SpringMVC 上传文件，配置允许上传的文件后缀再 systemConfig.xml 的attachmentFile.allowUploadSuffix.suffix节点
     *
     * @param filePath      上传后的文件所在目录、路径，如 "jar/file/"
     * @param multipartFile SpringMVC接收的 {@link org.springframework.web.multipart.MultipartFile},若是有上传文件，会自动转化为{@link org.springframework.web.multipart.MultipartFile}保存
     * @return {@link cn.edu.sjtu.gateway.vm.vo.UploadFileVO} 若成功，则上传了文件并且上传成功
     */
    public static UploadFileVO uploadFile(String filePath, MultipartFile multipartFile) {
        return uploadFileVOTransfer(FileUploadUtil.fileupload.upload(filePath, multipartFile));
    }


    /**
     * SpringMVC 上传图片文件，配置允许上传的文件后缀再 systemConfig.xml 的attachmentFile.allowUploadSuffix.suffix节点
     *
     * @param filePath      上传后的文件所在目录、路径，如 "jar/file/"
     * @param multipartFile SpringMVC接收的 {@link org.springframework.web.multipart.MultipartFile},若是有上传图片文件，会自动转化为{@link org.springframework.web.multipart.MultipartFile}保存
     * @param maxWidth      上传图片的最大宽度，若超过这个宽度，会对图片进行等比缩放为当前宽度。若传入0.则不启用此功能
     * @return {@link cn.edu.sjtu.gateway.vm.vo.UploadFileVO} 若成功，则上传了文件并且上传成功
     */
    public static UploadFileVO uploadImageByMultipartFile(String filePath, MultipartFile multipartFile, int maxWidth) {
        return uploadFileVOTransfer(FileUploadUtil.fileupload.uploadImage(filePath, multipartFile, maxWidth));
    }

    /**
     * SpringMVC 上传图片文件，配置允许上传的文件后缀再 systemConfig.xml 的attachmentFile.allowUploadSuffix.suffix节点
     *
     * @param filePath      上传后的文件所在目录、路径，如 "jar/file/"
     * @param multipartFile SpringMVC接收的 {@link org.springframework.web.multipart.MultipartFile},若是有上传图片文件，会自动转化为{@link org.springframework.web.multipart.MultipartFile}保存
     * @return {@link cn.edu.sjtu.gateway.vm.vo.UploadFileVO} 若成功，则上传了文件并且上传成功
     */
    public static UploadFileVO uploadImageByMultipartFile(String filePath, MultipartFile multipartFile) {
        return uploadImageByMultipartFile(filePath, multipartFile, 0);
    }

    /**
     * 上传文件
     *
     * @param filePath    上传后的文件所在的目录、路径，如 "jar/file/"
     * @param inputStream 要上传的文件的数据流
     * @param fileSuffix  上传的文件的后缀名
     * @return {@link cn.edu.sjtu.gateway.vm.vo.UploadFileVO}
     */
    public static UploadFileVO uploadFileByInputStream(String filePath, InputStream inputStream, String fileSuffix) {
        return uploadFileVOTransfer(FileUploadUtil.fileupload.upload(filePath, inputStream, fileSuffix));
    }

    /**
     * 上传图片文件
     *
     * @param filePath    上传后的文件所在的目录、路径，如 "jar/file/"
     * @param inputStream 图片的数据流
     * @param fileSuffix  图片的后缀名
     * @param maxWidth    上传图片的最大宽度，若超过这个宽度，会对图片进行等比缩放为当前宽度
     * @return {@link cn.edu.sjtu.gateway.vm.vo.UploadFileVO}
     */
    public static UploadFileVO uploadImageByInputStream(String filePath, InputStream inputStream, String fileSuffix, int maxWidth) {
        return uploadFileVOTransfer(FileUploadUtil.fileupload.uploadImage(filePath, inputStream, fileSuffix, maxWidth));
    }


    /**
     * 上传文件
     *
     * @param filePath    上传后的文件所在的目录、路径，如 "jar/file/"
     * @param fileName    上传的文件名，如“xnx3.jar”；主要拿里面的后缀名。也可以直接传入文件的后缀名如“.jar。新的文件名会是自动生成的 uuid+后缀”
     * @param inputStream {@link java.io.InputStream}
     * @return 若失败，返回null
     */
    public static UploadFileVO put(String filePath, String fileName, InputStream inputStream) {
        return uploadFileVOTransfer(FileUploadUtil.fileupload.upload(filePath, fileName, inputStream));
    }


    /**
     * SpringMVC 上传图片文件，配置允许上传的文件后缀再 systemConfig.xml 的AttachmentFile节点
     *
     * @param filePath     上传后的文件所在的目录、路径，如 "jar/file/"
     * @param request      SpringMVC接收的 {@link org.springframework.web.multipart.MultipartFile},若是有上传图片文件，会自动转化为{@link org.springframework.web.multipart.MultipartFile}保存
     * @param formFileName form表单上传的单个图片文件，表单里上传文件的文件名
     * @param maxWidth     上传图片的最大宽度，若超过这个宽度，会对图片进行等比缩放为当前宽度。
     * @return {@link cn.edu.sjtu.gateway.vm.vo.UploadFileVO} 若成功，则上传了文件并且上传成功
     */
    public static UploadFileVO uploadImage(String filePath, HttpServletRequest request, String formFileName, int maxWidth) {
        return uploadFileVOTransfer(FileUploadUtil.fileupload.uploadImage(filePath, request, formFileName, maxWidth));
    }

    /**
     * SpringMVC 上传文件，配置允许上传的文件后缀再 systemConfig.xml 的AttachmentFile节点
     *
     * @param filePath     上传后的文件所在的目录、路径，如 "jar/file/"
     * @param request      SpringMVC接收的 {@link org.springframework.web.multipart.MultipartFile},若是有上传文件，会自动转化为{@link org.springframework.web.multipart.MultipartFile}保存
     * @param formFileName form表单上传的单个文件，表单里上传文件的文件名
     * @return {@link cn.edu.sjtu.gateway.vm.vo.UploadFileVO} 若成功，则上传了文件并且上传成功
     */
    public static UploadFileVO uploadFile(String filePath, HttpServletRequest request, String formFileName) {
        return uploadFileVOTransfer(FileUploadUtil.fileupload.upload(filePath, request, formFileName));
    }

    /**
     * 获取某个目录（文件夹）占用空间的大小
     *
     * @param path 要计算的目录(文件夹)，如 jar/file/
     * @return 计算出来的大小。单位：字节，B。  千分之一KB
     */
    public static long getDirectorySize(String path) {
        return FileUploadUtil.fileupload.getDirectorySize(path);
    }

    public static UploadFileVO uploadFileVOTransfer(NaiiUploadFileVO inputVO) {
        UploadFileVO vo = new UploadFileVO();
        BeanUtils.copyProperties(inputVO, vo);
        return vo;
    }

}
