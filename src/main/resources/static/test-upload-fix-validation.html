<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传接口修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .card h3 {
            margin-top: 0;
            color: #2d3436;
            border-bottom: 3px solid #74b9ff;
            padding-bottom: 10px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #74b9ff;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .status-success { background-color: #00b894; }
        .status-error { background-color: #e17055; }
        .status-warning { background-color: #fdcb6e; }
        .status-info { background-color: #74b9ff; }
        
        .interface-path {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #636e72;
            margin-top: 5px;
        }
        
        .interface-name {
            font-weight: bold;
            color: #2d3436;
        }
        
        .btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
        }
        
        .log-container {
            background: #2d3436;
            color: #ddd;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #00b894; }
        .log-error { color: #e17055; }
        .log-warning { color: #fdcb6e; }
        .log-info { color: #74b9ff; }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #74b9ff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #636e72;
            font-size: 0.9em;
        }
        
        .test-section {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2d3436;
        }
        
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #74b9ff;
            border-radius: 8px;
            background: white;
        }
        
        .result-display {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        
        .result-success {
            background: #d1f2eb;
            border: 1px solid #00b894;
            color: #00695c;
        }
        
        .result-error {
            background: #ffeaa7;
            border: 1px solid #e17055;
            color: #d63031;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 上传接口修复验证</h1>
        <p>验证上传接口统一修复的效果</p>
    </div>

    <!-- 控制面板 -->
    <div class="card">
        <h3>🎛️ 控制面板</h3>
        <button class="btn" onclick="checkAllInterfaces()">🔍 检查所有接口</button>
        <button class="btn" onclick="runFixScript()">🔧 运行修复脚本</button>
        <button class="btn btn-success" onclick="testUploadFunctions()">🧪 测试上传功能</button>
        <button class="btn btn-warning" onclick="clearLogs()">🗑️ 清空日志</button>
        
        <div class="summary-stats">
            <div class="stat-item">
                <div class="stat-number" id="totalInterfaces">0</div>
                <div class="stat-label">总接口数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="availableInterfaces">0</div>
                <div class="stat-label">可用接口</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="fixedPaths">0</div>
                <div class="stat-label">已修复路径</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="testsPassed">0</div>
                <div class="stat-label">测试通过</div>
            </div>
        </div>
    </div>

    <!-- 接口状态 -->
    <div class="card">
        <h3>📊 接口状态</h3>
        <div id="interfaceStatus" class="status-grid">
            <div class="status-item">
                <span class="status-indicator status-info"></span>
                <div>
                    <div class="interface-name">点击"检查所有接口"开始检查</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 修复日志 -->
    <div class="card">
        <h3>📝 修复日志</h3>
        <div id="logContainer" class="log-container">
            <div class="log-entry log-info">[INFO] 等待开始修复验证...</div>
        </div>
    </div>

    <!-- 上传测试 -->
    <div class="card">
        <h3>🧪 上传功能测试</h3>
        <div class="test-section">
            <div class="form-group">
                <label for="testFile">选择测试文件:</label>
                <input type="file" id="testFile" accept="image/*">
            </div>
            <button class="btn" onclick="testSiteImageUpload()">测试站点图片上传</button>
            <button class="btn" onclick="testGeneralFileUpload()">测试通用文件上传</button>
            <button class="btn" onclick="testTemplateImageUpload()">测试模板图片上传</button>
            
            <div id="testResults" class="result-display" style="display: none;">
                等待测试结果...
            </div>
        </div>
    </div>

    <!-- 引入修复脚本 -->
    <script src="/js/url-fix.js"></script>
    <script>
        // 统计数据
        let stats = {
            totalInterfaces: 0,
            availableInterfaces: 0,
            fixedPaths: 0,
            testsPassed: 0
        };

        // 上传接口列表
        const UPLOAD_INTERFACES = [
            { name: '站点图片上传', path: '/sites/uploadImage.naii' },
            { name: '富文本图片上传', path: '/sites/tinymceUploadImage.naii' },
            { name: 'TinyMCE图片上传', path: '/sites/uploadImageTinymce.naii' },
            { name: 'TinyMCE文件上传', path: '/sites/uploadImageTinymceFile.naii' },
            { name: '通用文件上传', path: '/upload/file.naii' },
            { name: 'TinyMCE通用上传', path: '/upload/tinymce.naii' },
            { name: 'Markdown图片上传', path: '/upload/markdown.naii' },
            { name: '模板图片上传', path: '/template/uploadImage.naii' },
            { name: '模板导入上传', path: '/template/uploadImportTemplate.naii' },
            { name: '插件预览上传', path: '/plugin/templateDevelop/uploadPreview.naii' }
        ];

        // 日志函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新统计
        function updateStats() {
            document.getElementById('totalInterfaces').textContent = stats.totalInterfaces;
            document.getElementById('availableInterfaces').textContent = stats.availableInterfaces;
            document.getElementById('fixedPaths').textContent = stats.fixedPaths;
            document.getElementById('testsPassed').textContent = stats.testsPassed;
        }

        // 检查所有接口
        async function checkAllInterfaces() {
            addLog('开始检查所有上传接口...', 'info');
            
            const statusContainer = document.getElementById('interfaceStatus');
            statusContainer.innerHTML = '';
            
            stats.totalInterfaces = UPLOAD_INTERFACES.length;
            stats.availableInterfaces = 0;
            
            for (const interface of UPLOAD_INTERFACES) {
                try {
                    const response = await fetch(interface.path, { method: 'HEAD' });
                    const status = response.status;
                    
                    let statusClass, statusText;
                    if (status === 405) {
                        statusClass = 'status-success';
                        statusText = '✅ 可用 (Method Not Allowed)';
                        stats.availableInterfaces++;
                        addLog(`接口可用: ${interface.name} - ${interface.path}`, 'success');
                    } else if (status === 404) {
                        statusClass = 'status-error';
                        statusText = '❌ 不存在';
                        addLog(`接口不存在: ${interface.name} - ${interface.path}`, 'error');
                    } else if (status >= 200 && status < 300) {
                        statusClass = 'status-success';
                        statusText = '✅ 可用';
                        stats.availableInterfaces++;
                        addLog(`接口可用: ${interface.name} - ${interface.path}`, 'success');
                    } else {
                        statusClass = 'status-warning';
                        statusText = `⚠️ 状态码: ${status}`;
                        addLog(`接口异常: ${interface.name} - ${interface.path} (${status})`, 'warning');
                    }
                    
                    const statusItem = document.createElement('div');
                    statusItem.className = 'status-item';
                    statusItem.innerHTML = `
                        <span class="status-indicator ${statusClass}"></span>
                        <div>
                            <div class="interface-name">${interface.name}</div>
                            <div class="interface-path">${interface.path}</div>
                            <div style="font-size: 12px; color: #636e72;">${statusText}</div>
                        </div>
                    `;
                    statusContainer.appendChild(statusItem);
                    
                } catch (error) {
                    const statusItem = document.createElement('div');
                    statusItem.className = 'status-item';
                    statusItem.innerHTML = `
                        <span class="status-indicator status-error"></span>
                        <div>
                            <div class="interface-name">${interface.name}</div>
                            <div class="interface-path">${interface.path}</div>
                            <div style="font-size: 12px; color: #636e72;">❌ 网络错误</div>
                        </div>
                    `;
                    statusContainer.appendChild(statusItem);
                    addLog(`网络错误: ${interface.name} - ${interface.path}`, 'error');
                }
            }
            
            updateStats();
            addLog(`接口检查完成: ${stats.availableInterfaces}/${stats.totalInterfaces} 可用`, 'info');
        }

        // 运行修复脚本
        function runFixScript() {
            addLog('开始运行上传接口修复脚本...', 'info');
            
            if (window.UrlFixer) {
                try {
                    window.UrlFixer.fixUploadPaths();
                    stats.fixedPaths++;
                    addLog('URL修复脚本执行完成', 'success');
                } catch (error) {
                    addLog(`URL修复脚本执行失败: ${error.message}`, 'error');
                }
            }
            
            if (window.UploadInterfaceFixer) {
                try {
                    window.UploadInterfaceFixer.fix();
                    stats.fixedPaths++;
                    addLog('上传接口修复脚本执行完成', 'success');
                } catch (error) {
                    addLog(`上传接口修复脚本执行失败: ${error.message}`, 'error');
                }
            }
            
            updateStats();
        }

        // 测试上传功能
        function testUploadFunctions() {
            addLog('开始测试上传功能...', 'info');
            
            // 检查Layui是否可用
            if (window.layui) {
                addLog('Layui上传组件可用', 'success');
                stats.testsPassed++;
            } else {
                addLog('Layui上传组件不可用', 'warning');
            }
            
            // 检查jQuery是否可用
            if (window.jQuery) {
                addLog('jQuery AJAX上传可用', 'success');
                stats.testsPassed++;
            } else {
                addLog('jQuery不可用', 'warning');
            }
            
            // 检查TinyMCE是否可用
            if (window.tinymce) {
                addLog('TinyMCE编辑器可用', 'success');
                stats.testsPassed++;
            } else {
                addLog('TinyMCE编辑器不可用', 'warning');
            }
            
            updateStats();
            addLog('上传功能测试完成', 'info');
        }

        // 测试站点图片上传
        function testSiteImageUpload() {
            const fileInput = document.getElementById('testFile');
            const resultDiv = document.getElementById('testResults');
            
            if (!fileInput.files[0]) {
                showTestResult('请先选择一个测试文件', false);
                return;
            }
            
            const formData = new FormData();
            formData.append('image', fileInput.files[0]);
            
            addLog('开始测试站点图片上传...', 'info');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在上传...';
            resultDiv.className = 'result-display';
            
            fetch('/sites/uploadImage.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.result === 1) {
                    showTestResult(`上传成功: ${data.info || '文件已上传'}`, true);
                    addLog('站点图片上传测试成功', 'success');
                    stats.testsPassed++;
                } else {
                    showTestResult(`上传失败: ${data.info || '未知错误'}`, false);
                    addLog('站点图片上传测试失败', 'error');
                }
                updateStats();
            })
            .catch(error => {
                showTestResult(`网络错误: ${error.message}`, false);
                addLog(`站点图片上传测试出错: ${error.message}`, 'error');
            });
        }

        // 测试通用文件上传
        function testGeneralFileUpload() {
            const fileInput = document.getElementById('testFile');
            const resultDiv = document.getElementById('testResults');
            
            if (!fileInput.files[0]) {
                showTestResult('请先选择一个测试文件', false);
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('type', 'image');
            
            addLog('开始测试通用文件上传...', 'info');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在上传...';
            resultDiv.className = 'result-display';
            
            fetch('/upload/file.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.result === 1) {
                    showTestResult(`上传成功: ${data.info || '文件已上传'}`, true);
                    addLog('通用文件上传测试成功', 'success');
                    stats.testsPassed++;
                } else {
                    showTestResult(`上传失败: ${data.info || '未知错误'}`, false);
                    addLog('通用文件上传测试失败', 'error');
                }
                updateStats();
            })
            .catch(error => {
                showTestResult(`网络错误: ${error.message}`, false);
                addLog(`通用文件上传测试出错: ${error.message}`, 'error');
            });
        }

        // 测试模板图片上传
        function testTemplateImageUpload() {
            const fileInput = document.getElementById('testFile');
            const resultDiv = document.getElementById('testResults');
            
            if (!fileInput.files[0]) {
                showTestResult('请先选择一个测试文件', false);
                return;
            }
            
            const formData = new FormData();
            formData.append('image', fileInput.files[0]);
            
            addLog('开始测试模板图片上传...', 'info');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在上传...';
            resultDiv.className = 'result-display';
            
            fetch('/template/uploadImage.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.result === 1) {
                    showTestResult(`上传成功: ${data.info || '文件已上传'}`, true);
                    addLog('模板图片上传测试成功', 'success');
                    stats.testsPassed++;
                } else {
                    showTestResult(`上传失败: ${data.info || '未知错误'}`, false);
                    addLog('模板图片上传测试失败', 'error');
                }
                updateStats();
            })
            .catch(error => {
                showTestResult(`网络错误: ${error.message}`, false);
                addLog(`模板图片上传测试出错: ${error.message}`, 'error');
            });
        }

        // 显示测试结果
        function showTestResult(message, success) {
            const resultDiv = document.getElementById('testResults');
            resultDiv.style.display = 'block';
            resultDiv.textContent = message;
            resultDiv.className = `result-display ${success ? 'result-success' : 'result-error'}`;
        }

        // 清空日志
        function clearLogs() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '<div class="log-entry log-info">[INFO] 日志已清空</div>';
            
            // 重置统计
            stats = {
                totalInterfaces: 0,
                availableInterfaces: 0,
                fixedPaths: 0,
                testsPassed: 0
            };
            updateStats();
        }

        // 页面加载完成后自动检查
        window.onload = function() {
            addLog('页面加载完成，开始初始化...', 'info');
            updateStats();
            
            // 延迟执行检查，确保所有脚本都已加载
            setTimeout(() => {
                checkAllInterfaces();
            }, 1000);
        };
    </script>
</body>
</html>
