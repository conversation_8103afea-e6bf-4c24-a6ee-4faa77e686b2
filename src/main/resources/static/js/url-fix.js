/**
 * URL修复工具 - 修复上传接口路径和URL编码问题
 * 解决JAR包部署时的路径映射问题
 */
(function() {
    'use strict';
    
    console.log('🔧 URL修复工具已加载');
    
    // 修复URL编码问题
    function fixUrlEncoding() {
        console.log('🔍 开始检查URL编码问题...');
        
        // 检查当前URL是否有编码问题
        var currentUrl = window.location.href;
        var decodedUrl = decodeURIComponent(currentUrl);
        
        if (currentUrl !== decodedUrl && (currentUrl.includes('%7B') || currentUrl.includes('%7D'))) {
            console.log('⚠️ 发现URL编码问题:', currentUrl);
            console.log('🔄 重定向到正确URL:', decodedUrl);
            window.location.href = decodedUrl;
            return;
        }
        
        // 检查页面中的链接
        var links = document.querySelectorAll('a[href*="%7B"], a[href*="%7D"]');
        links.forEach(function(link) {
            var href = link.getAttribute('href');
            var decodedHref = decodeURIComponent(href);
            if (href !== decodedHref) {
                link.setAttribute('href', decodedHref);
                console.log('✅ 修复链接URL编码:', href, '->', decodedHref);
            }
        });
        
        console.log('✅ URL编码检查完成');
    }
    
    // 修复上传接口路径
    function fixUploadPaths() {
        console.log('🔧 开始修复上传接口路径...');
        
        // 上传接口路径映射表
        var uploadPathMappings = {
            // 站点相关上传接口
            'uploadImage.naii': '/sites/uploadImage.naii',
            'tinymceUploadImage.naii': '/sites/tinymceUploadImage.naii',
            'uploadImageTinymce.naii': '/sites/uploadImageTinymce.naii',
            'uploadImageTinymceFile.naii': '/sites/uploadImageTinymceFile.naii',
            
            // 通用文件上传接口
            'upload/file.naii': '/upload/file.naii',
            'upload/tinymce.naii': '/upload/tinymce.naii',
            'upload/markdown.naii': '/upload/markdown.naii',
            
            // 模板相关上传接口
            'template/uploadImage.naii': '/template/uploadImage.naii',
            'uploadPreview.naii': '/plugin/templateDevelop/uploadPreview.naii'
        };
        
        // 检查页面中的上传表单
        var uploadForms = document.querySelectorAll('form[action*="upload"]');
        uploadForms.forEach(function(form) {
            var action = form.getAttribute('action');
            console.log('检查上传表单action:', action);
            
            // 修复表单action路径
            for (var pattern in uploadPathMappings) {
                if (action && action.includes(pattern)) {
                    var correctPath = uploadPathMappings[pattern];
                    if (action !== correctPath) {
                        form.setAttribute('action', correctPath);
                        console.log('✅ 修复表单action:', action, '->', correctPath);
                    }
                    break;
                }
            }
        });
        
        // 检查Layui上传组件
        if (window.layui && window.layui.upload) {
            console.log('检查Layui上传组件配置...');
            // 这里可以添加Layui上传组件的路径检查
        }
        
        // 检查AJAX上传请求
        if (window.jQuery) {
            var originalAjax = jQuery.ajax;
            jQuery.ajax = function(options) {
                if (options.url && options.url.includes('upload')) {
                    console.log('检查AJAX上传请求:', options.url);
                    
                    // 修复AJAX请求URL
                    for (var pattern in uploadPathMappings) {
                        if (options.url.includes(pattern)) {
                            var correctPath = uploadPathMappings[pattern];
                            if (options.url !== correctPath && !options.url.startsWith(correctPath)) {
                                // 保留查询参数
                                var queryString = '';
                                var questionMarkIndex = options.url.indexOf('?');
                                if (questionMarkIndex > -1) {
                                    queryString = options.url.substring(questionMarkIndex);
                                }
                                options.url = correctPath + queryString;
                                console.log('✅ 修复AJAX上传URL:', options.url);
                            }
                            break;
                        }
                    }
                }
                return originalAjax.call(this, options);
            };
        }
        
        console.log('✅ 上传接口路径修复完成');
    }
    
    // 检查静态资源访问
    function checkStaticResources() {
        console.log('📁 检查静态资源访问...');
        
        // 检查图片资源
        var images = document.querySelectorAll('img[src*="/site/"], img[src*="/uploads/"]');
        images.forEach(function(img) {
            var src = img.getAttribute('src');
            if (src && !src.startsWith('http') && !src.startsWith('//')) {
                // 确保静态资源路径正确
                if (!src.startsWith('/')) {
                    img.setAttribute('src', '/' + src);
                    console.log('✅ 修复图片路径:', src, '->', '/' + src);
                }
            }
        });
        
        console.log('✅ 静态资源检查完成');
    }
    
    // 页面加载完成后执行修复
    function initUrlFixer() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                fixUrlEncoding();
                fixUploadPaths();
                checkStaticResources();
            });
        } else {
            fixUrlEncoding();
            fixUploadPaths();
            checkStaticResources();
        }
    }
    
    // 监听动态内容变化
    function observeContentChanges() {
        if (window.MutationObserver) {
            var observer = new MutationObserver(function(mutations) {
                var shouldRecheck = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        for (var i = 0; i < mutation.addedNodes.length; i++) {
                            var node = mutation.addedNodes[i];
                            if (node.nodeType === 1) { // Element node
                                if (node.tagName === 'FORM' || node.querySelector('form')) {
                                    shouldRecheck = true;
                                    break;
                                }
                            }
                        }
                    }
                });
                
                if (shouldRecheck) {
                    console.log('🔄 检测到新的表单元素，重新检查...');
                    fixUploadPaths();
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }
    
    // 导出修复函数供外部调用
    window.UrlFixer = {
        fixUrlEncoding: fixUrlEncoding,
        fixUploadPaths: fixUploadPaths,
        checkStaticResources: checkStaticResources,
        init: initUrlFixer
    };
    
    // 自动初始化
    initUrlFixer();
    observeContentChanges();
    
})();
