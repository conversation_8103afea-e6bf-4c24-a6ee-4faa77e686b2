<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一上传接口测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .test-card h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        input[type="file"], input[type="text"], input[type="number"], select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        input[type="file"]:focus, input[type="text"]:focus, input[type="number"]:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            min-height: 60px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
        .status-unknown { background-color: #6c757d; }
        
        .endpoint-path {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #666;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .upload-preview {
            max-width: 200px;
            max-height: 200px;
            margin-top: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .summary-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-top: 20px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 统一上传接口测试</h1>
        <p>测试所有上传接口的功能和路径映射</p>
    </div>

    <div class="summary-card">
        <h3>📊 接口状态总览</h3>
        <button onclick="checkAllEndpoints()">🔍 检查所有接口状态</button>
        <button onclick="runAllTests()">🧪 运行所有测试</button>
        <button onclick="clearAllResults()">🗑️ 清空结果</button>
        
        <div id="endpointStatus" class="result info">
            点击"检查所有接口状态"按钮开始检查...
        </div>
        
        <div class="summary-stats">
            <div class="stat-item">
                <div class="stat-number" id="totalEndpoints">0</div>
                <div class="stat-label">总接口数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="availableEndpoints">0</div>
                <div class="stat-label">可用接口</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="errorEndpoints">0</div>
                <div class="stat-label">错误接口</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="successfulUploads">0</div>
                <div class="stat-label">成功上传</div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 通用文件上传测试 -->
        <div class="test-card">
            <h3>📁 通用文件上传</h3>
            <form id="generalFileForm">
                <div class="form-group">
                    <label for="generalFile">选择文件:</label>
                    <input type="file" id="generalFile" name="file" accept="*/*">
                </div>
                <div class="form-group">
                    <label for="fileType">文件类型:</label>
                    <select id="fileType">
                        <option value="file">普通文件</option>
                        <option value="image">图片文件</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="generalFileSize">大小限制 (MB):</label>
                    <input type="number" id="generalFileSize" value="10" min="1" max="100">
                </div>
                <button type="button" onclick="uploadGeneralFile()">📤 上传到 /upload/file.naii</button>
            </form>
            <div class="progress-bar" id="generalFileProgress" style="display: none;">
                <div class="progress-fill"></div>
            </div>
            <div id="generalFileResult" class="result info">等待上传...</div>
        </div>

        <!-- 站点图片上传测试 -->
        <div class="test-card">
            <h3>🖼️ 站点图片上传</h3>
            <form id="siteImageForm">
                <div class="form-group">
                    <label for="siteImage">选择图片:</label>
                    <input type="file" id="siteImage" name="image" accept="image/*">
                </div>
                <button type="button" onclick="uploadSiteImage()">📤 上传到 /sites/uploadImage.naii</button>
            </form>
            <div class="progress-bar" id="siteImageProgress" style="display: none;">
                <div class="progress-fill"></div>
            </div>
            <div id="siteImageResult" class="result info">等待上传...</div>
        </div>

        <!-- TinyMCE文件上传测试 -->
        <div class="test-card">
            <h3>📝 TinyMCE文件上传</h3>
            <form id="tinymceFileForm">
                <div class="form-group">
                    <label for="tinymceFile">选择文件:</label>
                    <input type="file" id="tinymceFile" name="file" accept="*/*">
                </div>
                <div class="form-group">
                    <label for="tinymceFileSize">大小限制 (MB):</label>
                    <input type="number" id="tinymceFileSize" value="10" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="tinymceFileExts">允许扩展名:</label>
                    <input type="text" id="tinymceFileExts" value=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx">
                </div>
                <button type="button" onclick="uploadTinymceFile()">📤 上传到 /sites/uploadImageTinymceFile.naii</button>
            </form>
            <div class="progress-bar" id="tinymceFileProgress" style="display: none;">
                <div class="progress-fill"></div>
            </div>
            <div id="tinymceFileResult" class="result info">等待上传...</div>
        </div>

        <!-- TinyMCE图片上传测试 -->
        <div class="test-card">
            <h3>🎨 TinyMCE图片上传</h3>
            <form id="tinymceImageForm">
                <div class="form-group">
                    <label for="tinymceImage">选择图片:</label>
                    <input type="file" id="tinymceImage" name="file" accept="image/*">
                </div>
                <div class="form-group">
                    <label for="tinymceImageSize">大小限制 (MB):</label>
                    <input type="number" id="tinymceImageSize" value="5" min="1" max="50">
                </div>
                <div class="form-group">
                    <label for="tinymceImageExts">允许扩展名:</label>
                    <input type="text" id="tinymceImageExts" value=".jpg,.jpeg,.png,.gif">
                </div>
                <button type="button" onclick="uploadTinymceImage()">📤 上传到 /sites/uploadImageTinymce.naii</button>
            </form>
            <div class="progress-bar" id="tinymceImageProgress" style="display: none;">
                <div class="progress-fill"></div>
            </div>
            <div id="tinymceImageResult" class="result info">等待上传...</div>
        </div>

        <!-- 富文本图片上传测试 -->
        <div class="test-card">
            <h3>📄 富文本图片上传</h3>
            <form id="richTextForm">
                <div class="form-group">
                    <label for="richTextImage">选择图片:</label>
                    <input type="file" id="richTextImage" name="image" accept="image/*">
                </div>
                <button type="button" onclick="uploadRichTextImage()">📤 上传到 /sites/tinymceUploadImage.naii</button>
            </form>
            <div class="progress-bar" id="richTextProgress" style="display: none;">
                <div class="progress-fill"></div>
            </div>
            <div id="richTextResult" class="result info">等待上传...</div>
        </div>

        <!-- 模板图片上传测试 -->
        <div class="test-card">
            <h3>🎭 模板图片上传</h3>
            <form id="templateImageForm">
                <div class="form-group">
                    <label for="templateImage">选择图片:</label>
                    <input type="file" id="templateImage" name="image" accept="image/*">
                </div>
                <button type="button" onclick="uploadTemplateImage()">📤 上传到 /template/uploadImage.naii</button>
            </form>
            <div class="progress-bar" id="templateImageProgress" style="display: none;">
                <div class="progress-fill"></div>
            </div>
            <div id="templateImageResult" class="result info">等待上传...</div>
        </div>
    </div>

    <script src="/js/url-fix.js"></script>
    <script>
        // 统计变量
        let stats = {
            total: 0,
            available: 0,
            error: 0,
            successful: 0
        };

        // 所有上传接口列表
        const endpoints = [
            { path: '/upload/file.naii', name: '通用文件上传' },
            { path: '/upload/tinymce.naii', name: 'TinyMCE上传' },
            { path: '/upload/markdown.naii', name: 'Markdown上传' },
            { path: '/sites/uploadImage.naii', name: '站点图片上传' },
            { path: '/sites/tinymceUploadImage.naii', name: '富文本图片上传' },
            { path: '/sites/uploadImageTinymce.naii', name: 'TinyMCE图片上传' },
            { path: '/sites/uploadImageTinymceFile.naii', name: 'TinyMCE文件上传' },
            { path: '/template/uploadImage.naii', name: '模板图片上传' }
        ];

        // 更新统计显示
        function updateStats() {
            document.getElementById('totalEndpoints').textContent = stats.total;
            document.getElementById('availableEndpoints').textContent = stats.available;
            document.getElementById('errorEndpoints').textContent = stats.error;
            document.getElementById('successfulUploads').textContent = stats.successful;
        }

        // 显示进度条
        function showProgress(elementId) {
            const progressBar = document.getElementById(elementId);
            if (progressBar) {
                progressBar.style.display = 'block';
                const fill = progressBar.querySelector('.progress-fill');
                fill.style.width = '0%';
                
                // 模拟进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    fill.style.width = progress + '%';
                }, 100);
                
                return () => {
                    clearInterval(interval);
                    fill.style.width = '100%';
                    setTimeout(() => {
                        progressBar.style.display = 'none';
                    }, 500);
                };
            }
            return () => {};
        }

        // 检查所有接口状态
        async function checkAllEndpoints() {
            const statusDiv = document.getElementById('endpointStatus');
            statusDiv.innerHTML = '🔍 正在检查接口状态...';
            statusDiv.className = 'result info';
            
            stats.total = endpoints.length;
            stats.available = 0;
            stats.error = 0;
            
            let statusHtml = '<div class="status-grid">';
            
            for (const endpoint of endpoints) {
                try {
                    // 使用HEAD请求检查接口是否存在
                    const response = await fetch(endpoint.path, { method: 'HEAD' });
                    const status = response.status;
                    
                    let statusClass, statusText;
                    if (status === 405) {
                        // Method Not Allowed 说明接口存在但不支持HEAD请求
                        statusClass = 'status-success';
                        statusText = '✅ 可用';
                        stats.available++;
                    } else if (status === 404) {
                        statusClass = 'status-error';
                        statusText = '❌ 不存在';
                        stats.error++;
                    } else if (status >= 200 && status < 300) {
                        statusClass = 'status-success';
                        statusText = '✅ 可用';
                        stats.available++;
                    } else {
                        statusClass = 'status-pending';
                        statusText = `⚠️ ${status}`;
                        stats.error++;
                    }
                    
                    statusHtml += `
                        <div class="status-item">
                            <span class="status-indicator ${statusClass}"></span>
                            <div>
                                <div><strong>${endpoint.name}</strong></div>
                                <div class="endpoint-path">${endpoint.path}</div>
                                <div style="font-size: 12px; color: #666;">${statusText}</div>
                            </div>
                        </div>
                    `;
                } catch (error) {
                    statusHtml += `
                        <div class="status-item">
                            <span class="status-indicator status-error"></span>
                            <div>
                                <div><strong>${endpoint.name}</strong></div>
                                <div class="endpoint-path">${endpoint.path}</div>
                                <div style="font-size: 12px; color: #666;">❌ 网络错误</div>
                            </div>
                        </div>
                    `;
                    stats.error++;
                }
            }
            
            statusHtml += '</div>';
            statusDiv.innerHTML = statusHtml;
            statusDiv.className = 'result success';
            updateStats();
        }

        // 显示结果
        function showResult(elementId, success, message, data = null) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.className = `result ${success ? 'success' : 'error'}`;
            
            let html = `<strong>${success ? '✅ 成功' : '❌ 失败'}:</strong> ${message}`;
            
            if (data) {
                html += `<br><strong>响应数据:</strong><br><pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 10px; overflow-x: auto;">${JSON.stringify(data, null, 2)}</pre>`;
                
                if (data.url) {
                    html += `<br><strong>文件URL:</strong> <a href="${data.url}" target="_blank" style="color: #007bff; text-decoration: none;">${data.url}</a>`;
                    
                    // 如果是图片，显示预览
                    if (data.url.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i)) {
                        html += `<br><img src="${data.url}" class="upload-preview" alt="上传的图片" onerror="this.style.display='none';">`;
                    }
                }
            }
            
            resultDiv.innerHTML = html;
            
            if (success) {
                stats.successful++;
                updateStats();
            }
        }

        // 通用文件上传
        function uploadGeneralFile() {
            const formData = new FormData();
            const fileInput = document.getElementById('generalFile');
            const typeSelect = document.getElementById('fileType');
            const sizeInput = document.getElementById('generalFileSize');
            
            if (!fileInput.files[0]) {
                showResult('generalFileResult', false, '请选择要上传的文件');
                return;
            }
            
            const stopProgress = showProgress('generalFileProgress');
            
            formData.append('file', fileInput.files[0]);
            formData.append('type', typeSelect.value);
            formData.append('size', sizeInput.value + 'MB');
            
            fetch('/upload/file.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                stopProgress();
                showResult('generalFileResult', data.result === 1, data.info || '上传完成', data);
            })
            .catch(error => {
                stopProgress();
                showResult('generalFileResult', false, '网络错误: ' + error.message);
            });
        }

        // 站点图片上传
        function uploadSiteImage() {
            const formData = new FormData();
            const fileInput = document.getElementById('siteImage');
            
            if (!fileInput.files[0]) {
                showResult('siteImageResult', false, '请选择要上传的图片');
                return;
            }
            
            const stopProgress = showProgress('siteImageProgress');
            
            formData.append('image', fileInput.files[0]);
            
            fetch('/sites/uploadImage.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                stopProgress();
                showResult('siteImageResult', data.result === 1, data.info || '上传完成', data);
            })
            .catch(error => {
                stopProgress();
                showResult('siteImageResult', false, '网络错误: ' + error.message);
            });
        }

        // TinyMCE文件上传
        function uploadTinymceFile() {
            const formData = new FormData();
            const fileInput = document.getElementById('tinymceFile');
            const sizeInput = document.getElementById('tinymceFileSize');
            const extsInput = document.getElementById('tinymceFileExts');
            
            if (!fileInput.files[0]) {
                showResult('tinymceFileResult', false, '请选择要上传的文件');
                return;
            }
            
            const stopProgress = showProgress('tinymceFileProgress');
            
            formData.append('file', fileInput.files[0]);
            formData.append('size', sizeInput.value);
            formData.append('exts', extsInput.value);
            formData.append('accept', 'file');
            
            fetch('/sites/uploadImageTinymceFile.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                stopProgress();
                showResult('tinymceFileResult', data.result === 1, data.info || '上传完成', data);
            })
            .catch(error => {
                stopProgress();
                showResult('tinymceFileResult', false, '网络错误: ' + error.message);
            });
        }

        // TinyMCE图片上传
        function uploadTinymceImage() {
            const formData = new FormData();
            const fileInput = document.getElementById('tinymceImage');
            const sizeInput = document.getElementById('tinymceImageSize');
            const extsInput = document.getElementById('tinymceImageExts');
            
            if (!fileInput.files[0]) {
                showResult('tinymceImageResult', false, '请选择要上传的图片');
                return;
            }
            
            const stopProgress = showProgress('tinymceImageProgress');
            
            formData.append('file', fileInput.files[0]);
            formData.append('size', sizeInput.value);
            formData.append('exts', extsInput.value);
            
            fetch('/sites/uploadImageTinymce.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                stopProgress();
                showResult('tinymceImageResult', data.result === 1, data.info || '上传完成', data);
            })
            .catch(error => {
                stopProgress();
                showResult('tinymceImageResult', false, '网络错误: ' + error.message);
            });
        }

        // 富文本图片上传
        function uploadRichTextImage() {
            const formData = new FormData();
            const fileInput = document.getElementById('richTextImage');
            
            if (!fileInput.files[0]) {
                showResult('richTextResult', false, '请选择要上传的图片');
                return;
            }
            
            const stopProgress = showProgress('richTextProgress');
            
            formData.append('image', fileInput.files[0]);
            
            fetch('/sites/tinymceUploadImage.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                stopProgress();
                showResult('richTextResult', data.result === 1, data.info || '上传完成', data);
            })
            .catch(error => {
                stopProgress();
                showResult('richTextResult', false, '网络错误: ' + error.message);
            });
        }

        // 模板图片上传
        function uploadTemplateImage() {
            const formData = new FormData();
            const fileInput = document.getElementById('templateImage');
            
            if (!fileInput.files[0]) {
                showResult('templateImageResult', false, '请选择要上传的图片');
                return;
            }
            
            const stopProgress = showProgress('templateImageProgress');
            
            formData.append('image', fileInput.files[0]);
            
            fetch('/template/uploadImage.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                stopProgress();
                showResult('templateImageResult', data.result === 1, data.info || '上传完成', data);
            })
            .catch(error => {
                stopProgress();
                showResult('templateImageResult', false, '网络错误: ' + error.message);
            });
        }

        // 运行所有测试
        function runAllTests() {
            alert('请手动选择文件并点击各个上传按钮进行测试。自动化测试需要用户交互选择文件。');
        }

        // 清空所有结果
        function clearAllResults() {
            const resultElements = document.querySelectorAll('.result');
            resultElements.forEach(element => {
                element.className = 'result info';
                element.innerHTML = '等待上传...';
            });
            
            stats.successful = 0;
            updateStats();
        }

        // 页面加载完成后自动检查接口状态
        window.onload = function() {
            updateStats();
            checkAllEndpoints();
        };
    </script>
</body>
</html>
