<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传接口测试 - 修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"], input[type="text"], input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            min-height: 50px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 上传接口修复验证</h1>
        <p>此页面用于验证修复后的上传接口是否正常工作。</p>
        
        <div class="test-section">
            <h3>📋 接口状态检查</h3>
            <button onclick="checkEndpoints()">检查所有接口状态</button>
            <div id="endpointStatus" class="result info">
                点击按钮检查接口状态...
            </div>
        </div>

        <div class="test-section">
            <h3>📤 TinyMCE文件上传测试</h3>
            <form id="tinymceFileForm">
                <div class="form-group">
                    <label for="tinymceFile">选择文件:</label>
                    <input type="file" id="tinymceFile" name="file" accept="*/*">
                </div>
                <div class="form-group">
                    <label for="fileSize">文件大小限制 (MB):</label>
                    <input type="number" id="fileSize" value="10" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="fileExts">允许的文件扩展名:</label>
                    <input type="text" id="fileExts" value=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx">
                </div>
                <button type="button" onclick="uploadTinymceFile()">上传文件</button>
            </form>
            <div id="tinymceResult" class="result info">
                等待上传...
            </div>
        </div>

        <div class="test-section">
            <h3>🖼️ TinyMCE图片上传测试</h3>
            <form id="tinymceImageForm">
                <div class="form-group">
                    <label for="tinymceImage">选择图片:</label>
                    <input type="file" id="tinymceImage" name="file" accept="image/*">
                </div>
                <div class="form-group">
                    <label for="imageSize">图片大小限制 (MB):</label>
                    <input type="number" id="imageSize" value="5" min="1" max="50">
                </div>
                <div class="form-group">
                    <label for="imageExts">允许的图片扩展名:</label>
                    <input type="text" id="imageExts" value=".jpg,.jpeg,.png,.gif">
                </div>
                <button type="button" onclick="uploadTinymceImage()">上传图片</button>
            </form>
            <div id="tinymceImageResult" class="result info">
                等待上传...
            </div>
        </div>

        <div class="test-section">
            <h3>📷 通用图片上传测试</h3>
            <form id="generalImageForm">
                <div class="form-group">
                    <label for="generalImage">选择图片:</label>
                    <input type="file" id="generalImage" name="image" accept="image/*">
                </div>
                <button type="button" onclick="uploadGeneralImage()">上传图片</button>
            </form>
            <div id="generalImageResult" class="result info">
                等待上传...
            </div>
        </div>

        <div class="test-section">
            <h3>📝 富文本图片上传测试</h3>
            <form id="richTextForm">
                <div class="form-group">
                    <label for="richTextImage">选择图片:</label>
                    <input type="file" id="richTextImage" name="image" accept="image/*">
                </div>
                <button type="button" onclick="uploadRichTextImage()">上传图片</button>
            </form>
            <div id="richTextResult" class="result info">
                等待上传...
            </div>
        </div>
    </div>

    <script>
        // 检查接口状态
        async function checkEndpoints() {
            const endpoints = [
                '/sites/uploadImageTinymceFile.naii',
                '/sites/uploadImageTinymce.naii', 
                '/sites/uploadImage.naii',
                '/sites/tinymceUploadImage.naii'
            ];
            
            const statusDiv = document.getElementById('endpointStatus');
            statusDiv.innerHTML = '正在检查接口状态...';
            
            let statusHtml = '<h4>接口状态检查结果:</h4>';
            
            for (const endpoint of endpoints) {
                try {
                    // 使用HEAD请求检查接口是否存在
                    const response = await fetch(endpoint, { method: 'HEAD' });
                    const status = response.status;
                    
                    let statusClass, statusText;
                    if (status === 405) {
                        // Method Not Allowed 说明接口存在但不支持HEAD请求
                        statusClass = 'status-success';
                        statusText = '✅ 接口存在';
                    } else if (status === 404) {
                        statusClass = 'status-error';
                        statusText = '❌ 接口不存在';
                    } else {
                        statusClass = 'status-pending';
                        statusText = `⚠️ 状态码: ${status}`;
                    }
                    
                    statusHtml += `<div><span class="status-indicator ${statusClass}"></span>${endpoint} - ${statusText}</div>`;
                } catch (error) {
                    statusHtml += `<div><span class="status-indicator status-error"></span>${endpoint} - ❌ 网络错误</div>`;
                }
            }
            
            statusDiv.innerHTML = statusHtml;
        }

        // 显示结果
        function showResult(elementId, success, message, data = null) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.className = `result ${success ? 'success' : 'error'}`;
            
            let html = `<strong>${success ? '✅ 成功' : '❌ 失败'}:</strong> ${message}`;
            
            if (data) {
                html += `<br><strong>响应数据:</strong><br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                if (data.url) {
                    html += `<br><strong>文件URL:</strong> <a href="${data.url}" target="_blank">${data.url}</a>`;
                }
            }
            
            resultDiv.innerHTML = html;
        }

        // TinyMCE文件上传
        function uploadTinymceFile() {
            const form = document.getElementById('tinymceFileForm');
            const formData = new FormData();
            
            const fileInput = document.getElementById('tinymceFile');
            const sizeInput = document.getElementById('fileSize');
            const extsInput = document.getElementById('fileExts');
            
            if (!fileInput.files[0]) {
                showResult('tinymceResult', false, '请选择要上传的文件');
                return;
            }
            
            formData.append('file', fileInput.files[0]);
            formData.append('size', sizeInput.value);
            formData.append('exts', extsInput.value);
            formData.append('accept', 'file');
            
            fetch('/sites/uploadImageTinymceFile.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showResult('tinymceResult', data.result === 1, data.info || '上传完成', data);
            })
            .catch(error => {
                showResult('tinymceResult', false, '网络错误: ' + error.message);
            });
        }

        // TinyMCE图片上传
        function uploadTinymceImage() {
            const formData = new FormData();
            
            const fileInput = document.getElementById('tinymceImage');
            const sizeInput = document.getElementById('imageSize');
            const extsInput = document.getElementById('imageExts');
            
            if (!fileInput.files[0]) {
                showResult('tinymceImageResult', false, '请选择要上传的图片');
                return;
            }
            
            formData.append('file', fileInput.files[0]);
            formData.append('size', sizeInput.value);
            formData.append('exts', extsInput.value);
            
            fetch('/sites/uploadImageTinymce.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showResult('tinymceImageResult', data.result === 1, data.info || '上传完成', data);
            })
            .catch(error => {
                showResult('tinymceImageResult', false, '网络错误: ' + error.message);
            });
        }

        // 通用图片上传
        function uploadGeneralImage() {
            const formData = new FormData();
            
            const fileInput = document.getElementById('generalImage');
            
            if (!fileInput.files[0]) {
                showResult('generalImageResult', false, '请选择要上传的图片');
                return;
            }
            
            formData.append('image', fileInput.files[0]);
            
            fetch('/sites/uploadImage.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showResult('generalImageResult', data.result === 1, data.info || '上传完成', data);
            })
            .catch(error => {
                showResult('generalImageResult', false, '网络错误: ' + error.message);
            });
        }

        // 富文本图片上传
        function uploadRichTextImage() {
            const formData = new FormData();
            
            const fileInput = document.getElementById('richTextImage');
            
            if (!fileInput.files[0]) {
                showResult('richTextResult', false, '请选择要上传的图片');
                return;
            }
            
            formData.append('image', fileInput.files[0]);
            
            fetch('/sites/tinymceUploadImage.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showResult('richTextResult', data.result === 1, data.info || '上传完成', data);
            })
            .catch(error => {
                showResult('richTextResult', false, '网络错误: ' + error.message);
            });
        }

        // 页面加载完成后自动检查接口状态
        window.onload = function() {
            checkEndpoints();
        };
    </script>
</body>
</html>
