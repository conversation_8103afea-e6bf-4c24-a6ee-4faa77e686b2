# =============================================================================
# NAII Gateway 开发环境配置
# =============================================================================
# 说明：此文件只包含开发环境特有的配置项
# 公共配置请查看 application.yml 文件
# 特点：调试友好、性能要求低、日志详细
# =============================================================================

# 数据库连接配置
database:
  # 数据库服务器IP地址（开发环境使用本地数据库）
  ip: ${NAII_DATABASES_IP:127.0.0.1}

# =============================================================================
# 日志配置 - 开发环境详细日志
# =============================================================================
logging:
  level:
    # 项目业务代码日志级别
    cn:
      edu:
        sjtu: ${NAII_LOGGING_SJTU_LEVEL:debug}  # 项目业务日志：详细调试信息
    # 系统根日志级别
    root: ${NAII_LOGGING_LEVEL:info}              # 根日志：一般信息
    # 数据库相关日志
    sql: ${NAII_LOGGING_SQL_LEVEL:debug}          # SQL日志：显示所有SQL语句
    # Web请求日志
    web: ${NAII_LOGGING_WEB_LEVEL:debug}          # Web日志：显示请求详情
    # 第三方框架日志
    org:
      springframework:
        web: debug                              # Spring Web：详细调试
      hibernate:
        SQL: debug                              # Hibernate SQL：显示SQL语句
        type:
          descriptor:
            sql:
              BasicBinder: trace              # 参数绑定：显示SQL参数值

# =============================================================================
# 服务器配置 - 开发环境特定配置
# =============================================================================
server:
  tomcat:
    # 开发环境连接数配置（适中规模，满足开发需求）
    max-connections: 200      # 最大并发连接数
    threads:
      max: 200                # 最大线程数
      min-spare: 10           # 最小空闲线程数
  # JSP支持配置
  jsp-servlet:
    init-parameters:
      development: true       # 开发模式：启用热部署和调试功能

# =============================================================================
# Spring 框架配置 - 开发环境特定配置
# =============================================================================
spring:
  # 数据源配置
  datasource:
    # HikariCP 连接池配置（适中规模，便于调试）
    hikari:
      maximum-pool-size: 10           # 最大连接数：10个足够开发使用
      minimum-idle: 5                 # 最小空闲连接数
      connection-timeout: 30000       # 连接超时时间（30秒）
      idle-timeout: 600000            # 空闲连接超时时间（10分钟）
      max-lifetime: 1800000           # 连接最大生命周期（30分钟）
      leak-detection-threshold: 60000 # 连接泄漏检测阈值（1分钟）
      pool-name: NaiiDevHikariCP      # 连接池名称，便于监控和调试
    # MySQL 数据库连接URL（开发环境优化）
    url: jdbc:mysql://${database.ip}:3306/${database.name}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true
  
  # JPA/Hibernate 配置
  jpa:
    properties:
      hibernate:
        hbm2ddl:
          auto: update            # 自动更新表结构（开发环境适用）
        # 开发环境SQL调试配置
        format_sql: true          # 格式化SQL输出，便于阅读
        use_sql_comments: true    # 显示SQL注释
        generate_statistics: true # 生成性能统计信息
    # 在控制台显示SQL语句（开发环境开启）
    show-sql: true
  
  # Spring Boot 配置
  main:
    # 允许Bean定义覆盖（开发环境灵活性）
    allow-bean-definition-overriding: true
  
  # 文件上传配置
  servlet:
    multipart:
      # 开发环境文件上传限制（适中规模）
      max-file-size: ${NAII_FILE_SIZE:100MB}     # 单个文件最大100MB
      max-request-size: ${NAII_REQUEST_SIZE:100MB} # 整个请求最大100MB
      # 临时文件存储位置（使用系统临时目录）
      location: ${java.io.tmpdir}/naii-upload

# =============================================================================
# 文件上传配置 - 开发环境特定配置
# =============================================================================
fileupload:
  # 开发环境文件访问域名（本地开发服务器）
  domain: http://***********:8080/
  # 文件上传大小限制
  maxSize: 100MB

# =============================================================================
# VM 虚拟机配置 - 开发环境覆盖
# =============================================================================
vm:
  showsql: true   # 开发环境显示SQL（覆盖公共配置）
wm:
  showsql: true   # 兼容旧配置名

# =============================================================================
# NAII 系统自定义配置 - 开发环境特定配置
# =============================================================================
naii:
  request:
    frequency:
      url:
        # 开发环境不限制URL访问频率（便于调试和测试）
        delay: 0
      ip:
        # 开发环境IP请求限制（放宽限制）
        requestNumber: 100        # 单个IP每秒最多100个请求
        # 触发限制后的禁止时间（开发环境设置为5分钟）
      forbidTime: 300000          # 5分钟 = 5 * 60 * 1000毫秒

# =============================================================================
# 开发环境监控配置
# =============================================================================
management:
  endpoints:
    web:
      exposure:
        # 开发环境暴露所有监控端点，便于调试和监控
        include: health,info,metrics,env,configprops,beans
  endpoint:
    health:
      # 显示详细的健康检查信息（开发环境适用）
      show-details: always
