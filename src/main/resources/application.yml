# =============================================================================
# NAII Gateway 主配置文件
# =============================================================================
# 说明：此文件包含所有环境的公共配置
# 环境特定配置请在对应的 application-{环境名}.yml 中设置
# 支持的环境：dev(开发)、test(测试)、prod(生产)
# =============================================================================

spring:
  profiles:
    # 默认激活的环境配置（可通过环境变量 NAII_GATEWAY_PROFILE 覆盖）
    active: ${NAII_GATEWAY_PROFILE:test}

  # 数据库连接公共配置
  datasource:
    # MySQL数据库驱动
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 数据库用户名（可通过环境变量覆盖）
    username: ${NAII_DATABASES_USERNAME:root}
    # 数据库密码（可通过环境变量覆盖）
    password: ${NAII_DATABASES_PASSWORD:Passw0rd@_}

  # JPA/Hibernate 公共配置
  jpa:
    properties:
      hibernate:
        # 指定MySQL8方言，确保兼容性
        dialect: org.hibernate.dialect.MySQL8Dialect
        # 缓存配置（暂时关闭避免依赖问题）
        cache:
          use_second_level_cache: false  # 关闭二级缓存
          use_query_cache: false         # 关闭查询缓存

  # Spring Boot 兼容性配置
  main:
    # 允许循环依赖（Spring Boot 2.6+需要）
    allow-circular-references: true

  # Web MVC 公共配置
  mvc:
    # 静态资源访问路径
    static-path-pattern: /static/**

  # 文件上传公共配置
  servlet:
    multipart:
      # 文件大小阈值：超过2MB的文件将写入磁盘而非内存
      file-size-threshold: 2MB

# =============================================================================
# 服务器公共配置
# =============================================================================
server:
  # HTTP请求头最大大小（可通过环境变量覆盖）
  max-http-header-size: ${NAII_HEADER_SIZE:10MB}
  # 服务器端口（可通过环境变量覆盖）
  port: ${NAII_SERVER_PORT:8080}
  # Tomcat 容器配置
  tomcat:
    # POST请求最大大小
    max-http-form-post-size: ${NAII_POST_SIZE:10MB}
    # 连接超时时间（毫秒）
    connection-timeout: ${NAII_CONNECTION_TIMEOUT:60000}
  # JSP支持配置（JAR包运行模式）
  jsp-servlet:
    init-parameters:
      # Java编译版本
      compilerSourceVM: 17
      compilerTargetVM: 17

# =============================================================================
# API 接口配置
# =============================================================================
api:
  # API接口默认后缀
  suffix: .json

# =============================================================================
# 数据库公共配置
# =============================================================================
database:
  # 数据库名称（可通过环境变量覆盖）
  name: ${NAII_DATABASES_NAME:naii}

# =============================================================================
# URL 访问配置
# =============================================================================
url:
  # 网站访问后缀名
  suffix: .naii

# =============================================================================
# 文件上传公共配置
# =============================================================================
fileupload:
  # 允许上传的文件类型（按文件后缀名区分）
  allowUploadSuffix: png|jpg|jpeg|gif|bmp|flv|swf|mkv|avi|rm|rmvb|mpeg|mpg|ogg|ogv|mov|wmv|mp4|webm|mp3|wav|mid|rar|zip|tar|gz|7z|bz2|cab|iso|doc|docx|xls|xlsx|ppt|pptx|pdf|txt|md|xml
  # 文件存储配置
  storage:
    local:
      # 本地文件存储路径（相对路径）
      path: ./uploads/

# =============================================================================
# VM 虚拟机配置
# =============================================================================
vm:
  # 是否显示SQL语句（可通过环境变量覆盖）
  showsql: ${VM_SHOW_SQL:true}
# 兼容旧版本配置名
wm:
  showsql: ${VM_SHOW_SQL:true}

# =============================================================================
# NAII 系统自定义配置（公共部分）
# =============================================================================
naii:
  request:
    frequency:
      # 需要频率控制的请求类型（按文件后缀名）
      suffix: jsp,naii,json,html
      ip:
        # IP请求间隔时间（毫秒）
        delay: 1000