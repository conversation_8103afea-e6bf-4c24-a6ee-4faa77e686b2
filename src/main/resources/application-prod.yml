# =============================================================================
# NAII Gateway 生产环境配置
# =============================================================================
# 说明：此文件只包含生产环境特有的配置项
# 公共配置请查看 application.yml 文件
# 特点：高性能、高安全性、严格限制、最小化日志
# =============================================================================

# 数据库连接配置
database:
  # 数据库服务器IP地址（生产环境数据库服务器）
  ip: ${NAII_DATABASES_IP:127.0.0.1}

# =============================================================================
# 日志配置 - 生产环境最小化日志
# =============================================================================
logging:
  level:
    # 项目业务代码日志级别
    cn:
      edu:
        sjtu: ${NAII_LOGGING_SJTU_LEVEL:info}    # 项目业务日志：一般信息
    # 系统根日志级别
    root: ${NAII_LOGGING_LEVEL:warn}                # 根日志：警告及以上
    # 数据库相关日志
    sql: ${NAII_LOGGING_SQL_LEVEL:error}            # SQL日志：只记录错误
    # Web请求日志
    web: ${NAII_LOGGING_WEB_LEVEL:error}            # Web日志：只记录错误
    # 第三方框架日志
    org:
      springframework: error                     # Spring框架：只记录错误
      hibernate: error                          # Hibernate：只记录错误
  # 生产环境日志文件配置
  file:
    name: /var/log/naii-gateway/application.log   # 日志文件路径
    max-size: 100MB                               # 单个日志文件最大大小
    max-history: 30                               # 保留日志文件数量（30天）

# =============================================================================
# 服务器配置 - 生产环境高性能配置
# =============================================================================
server:
  tomcat:
    # 生产环境连接超时设置（更长的超时时间）
    connection-timeout: 120000        # 2分钟连接超时
    # 生产环境高并发连接数配置
    max-connections: 10000            # 最大并发连接数：10000
    threads:
      max: 300                        # 最大线程数：300
      min-spare: 50                   # 最小空闲线程数：50
    # 处理大文件的配置
    max-swallow-size: 500MB           # 最大呑吐大小（处理大文件）
    # 生产环境性能优化参数
    accept-count: 1000                # 等待队列最大长度
    max-keep-alive-requests: 1000     # 最大保持连接请求数
  # JSP支持配置
  jsp-servlet:
    init-parameters:
      development: false              # 生产模式：关闭开发功能
# =============================================================================
# Spring 框架配置 - 生产环境特定配置
# =============================================================================
spring:
  # 数据源配置
  datasource:
    # HikariCP 连接池配置（高性能生产环境）
    hikari:
      maximum-pool-size: 30           # 最大连接数：30个适合高并发
      minimum-idle: 15                # 最小空闲连接数
      connection-timeout: 30000       # 连接超时时间（30秒）
      idle-timeout: 600000            # 空闲连接超时时间（10分钟）
      max-lifetime: 1800000           # 连接最大生命周期（30分钟）
      leak-detection-threshold: 60000 # 连接泄漏检测阈值（1分钟）
      # 生产环境连接池优化参数
      pool-name: NaiiProdHikariCP     # 连接池名称，便于监控
      connection-test-query: SELECT 1 # 连接测试查询
      validation-timeout: 5000        # 连接验证超时时间
    # MySQL 数据库连接URL（生产环境安全配置）
    url: jdbc:mysql://${database.ip}:3306/${database.name}?useUnicode=true&characterEncoding=utf-8&useSSL=true&requireSSL=true&verifyServerCertificate=false&serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true&cachePrepStmts=true&prepStmtCacheSize=250&prepStmtCacheSqlLimit=2048
    # 生产环境数据库密码（使用不同的默认密码）
    password: ${NAII_DATABASES_PASSWORD:naiiPassw0rd@_}
  # JPA/Hibernate 配置
  jpa:
    properties:
      hibernate:
        hbm2ddl:
          # 生产环境不自动修改表结构（安全考虑）
          auto: none
        # 生产环境性能优化配置
        format_sql: false         # 不格式化SQL输出
        use_sql_comments: false   # 不显示SQL注释
        generate_statistics: false # 不生成性能统计
        # 批处理优化参数
        jdbc:
          batch_size: 50          # 批处理大小
          order_inserts: true     # 排序插入操作
          order_updates: true     # 排序更新操作
    # 生产环境不在控制台显示SQL语句
    show-sql: false

  # 文件上传配置
  servlet:
    multipart:
      # 生产环境文件上传限制（大文件支持）
      max-file-size: ${NAII_FILE_SIZE:500MB}     # 单个文件最大500MB
      max-request-size: ${NAII_REQUEST_SIZE:500MB} # 整个请求最大500MB
      # 生产环境临时文件存储位置
      location: /tmp/naii-upload

# =============================================================================
# 文件上传配置 - 生产环境特定配置
# =============================================================================
fileupload:
  # 生产环境文件访问域名（HTTPS安全访问）
  domain: https://naii.sjtu.edu.cn/
  # 文件上传大小限制
  maxSize: 500MB

# =============================================================================
# VM 虚拟机配置 - 生产环境覆盖
# =============================================================================
vm:
  showsql: false  # 生产环境不显示SQL（覆盖公共配置）
wm:
  showsql: false  # 兼容旧配置名

# =============================================================================
# NAII 系统自定义配置 - 生产环境特定配置
# =============================================================================
naii:
  request:
    frequency:
      url:
        # 生产环境严格限制URL访问频率（防止恶意请求）
        delay: 500                # 每个URL请求间隔500毫秒
      ip:
        # 生产环境严格的IP请求限制
        requestNumber: 30         # 单个IP每秒最多30个请求
        # 触发限制后的禁止时间（生产环境设置为30分钟）
      forbidTime: 1800000         # 30分钟 = 30 * 60 * 1000毫秒

# =============================================================================
# 生产环境监控和管理配置
# =============================================================================
management:
  endpoints:
    web:
      exposure:
        # 生产环境只暴露最必要的监控端点（安全考虑）
        include: health,info
      base-path: /actuator        # 监控端点访问路径
  endpoint:
    health:
      show-details: never         # 不显示详细健康信息（安全考虑）
  server:
    port: 8081                    # 监控端点使用独立端口

# =============================================================================
# 生产环境安全配置
# =============================================================================
security:
  require-ssl: true               # 强制使用SSL/HTTPS