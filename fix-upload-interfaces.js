/**
 * 上传接口统一修复脚本
 * 用于在浏览器中运行，自动检测和修复页面中的上传接口路径
 */
(function() {
    'use strict';
    
    console.log('🔧 开始执行上传接口统一修复...');
    
    // 统一的上传接口映射表
    const UPLOAD_INTERFACE_MAPPINGS = {
        // 站点相关上传接口 - 统一到 /sites
        'uploadImage.naii': '/sites/uploadImage.naii',
        'tinymceUploadImage.naii': '/sites/tinymceUploadImage.naii',
        'uploadImageTinymce.naii': '/sites/uploadImageTinymce.naii',
        'uploadImageTinymceFile.naii': '/sites/uploadImageTinymceFile.naii',
        
        // 通用文件上传接口 - 统一到 /upload
        'upload/file.naii': '/upload/file.naii',
        'upload/tinymce.naii': '/upload/tinymce.naii',
        'upload/markdown.naii': '/upload/markdown.naii',
        
        // 模板相关上传接口 - 统一到 /template
        'template/uploadImage.naii': '/template/uploadImage.naii',
        'template/uploadImportTemplate.naii': '/template/uploadImportTemplate.naii',
        
        // 插件相关上传接口
        'uploadPreview.naii': '/plugin/templateDevelop/uploadPreview.naii'
    };
    
    // 需要修复的旧路径模式
    const OLD_PATTERNS = [
        // 动态路径模式
        /parent\.masterSiteUrl\s*\+\s*['"]upload\/file\.naii['"]/g,
        /parent\.masterSiteUrl\s*\+\s*['"]upload\/file\.naii\?type=image['"]/g,
        /masterSiteUrl\s*\+\s*['"]template\/uploadImage\.naii['"]/g,
        
        // 相对路径模式
        /['"]\.\/upload\/file\.naii['"]/g,
        /['"]\.\/sites\/uploadImage\.naii['"]/g,
        
        // 错误的绝对路径
        /['"]\/admin\/sites\/uploadImage\.naii['"]/g,
        /['"]\/admin\/sites\/uploadImageTinymceFile\.naii['"]/g
    ];
    
    // 修复函数
    function fixUploadInterfaces() {
        let fixCount = 0;
        
        console.log('📋 开始检查和修复上传接口...');
        
        // 1. 修复表单action属性
        const forms = document.querySelectorAll('form[action*="upload"]');
        forms.forEach(form => {
            const action = form.getAttribute('action');
            if (action) {
                for (const [pattern, correctPath] of Object.entries(UPLOAD_INTERFACE_MAPPINGS)) {
                    if (action.includes(pattern) && action !== correctPath) {
                        // 保留查询参数
                        const queryString = action.includes('?') ? action.substring(action.indexOf('?')) : '';
                        const newAction = correctPath + queryString;
                        form.setAttribute('action', newAction);
                        console.log(`✅ 修复表单action: ${action} -> ${newAction}`);
                        fixCount++;
                        break;
                    }
                }
            }
        });
        
        // 2. 修复Layui上传组件配置
        if (window.layui && window.layui.upload) {
            console.log('🔍 检查Layui上传组件配置...');
            
            // 重写layui.upload.render方法
            const originalRender = window.layui.upload.render;
            window.layui.upload.render = function(options) {
                if (options && options.url) {
                    const originalUrl = options.url;
                    
                    // 修复动态路径
                    if (originalUrl.includes('parent.masterSiteUrl')) {
                        if (originalUrl.includes('upload/file.naii')) {
                            options.url = '/upload/file.naii' + (originalUrl.includes('?') ? originalUrl.substring(originalUrl.indexOf('?')) : '');
                            console.log(`✅ 修复Layui上传URL: ${originalUrl} -> ${options.url}`);
                            fixCount++;
                        }
                    } else if (originalUrl.includes('masterSiteUrl')) {
                        if (originalUrl.includes('template/uploadImage.naii')) {
                            options.url = '/template/uploadImage.naii';
                            console.log(`✅ 修复Layui上传URL: ${originalUrl} -> ${options.url}`);
                            fixCount++;
                        }
                    } else {
                        // 修复其他路径
                        for (const [pattern, correctPath] of Object.entries(UPLOAD_INTERFACE_MAPPINGS)) {
                            if (originalUrl.includes(pattern) && originalUrl !== correctPath) {
                                const queryString = originalUrl.includes('?') ? originalUrl.substring(originalUrl.indexOf('?')) : '';
                                options.url = correctPath + queryString;
                                console.log(`✅ 修复Layui上传URL: ${originalUrl} -> ${options.url}`);
                                fixCount++;
                                break;
                            }
                        }
                    }
                }
                return originalRender.call(this, options);
            };
        }
        
        // 3. 修复TinyMCE配置
        if (window.tinymce) {
            console.log('🔍 检查TinyMCE上传配置...');
            
            // 重写tinymce.init方法
            const originalInit = window.tinymce.init;
            window.tinymce.init = function(config) {
                if (config) {
                    // 修复images_upload_url
                    if (config.images_upload_url) {
                        const originalUrl = config.images_upload_url;
                        for (const [pattern, correctPath] of Object.entries(UPLOAD_INTERFACE_MAPPINGS)) {
                            if (originalUrl.includes(pattern)) {
                                config.images_upload_url = correctPath;
                                console.log(`✅ 修复TinyMCE上传URL: ${originalUrl} -> ${config.images_upload_url}`);
                                fixCount++;
                                break;
                            }
                        }
                    }
                    
                    // 修复file_picker_callback中的上传URL
                    if (config.file_picker_callback) {
                        const originalCallback = config.file_picker_callback;
                        config.file_picker_callback = function(callback, value, meta) {
                            // 在这里可以修复文件选择器中的上传URL
                            return originalCallback.call(this, callback, value, meta);
                        };
                    }
                }
                return originalInit.call(this, config);
            };
        }
        
        // 4. 修复AJAX请求
        if (window.jQuery) {
            console.log('🔍 检查jQuery AJAX上传请求...');
            
            const originalAjax = window.jQuery.ajax;
            window.jQuery.ajax = function(options) {
                if (options && options.url && options.url.includes('upload')) {
                    const originalUrl = options.url;
                    
                    // 修复动态路径
                    if (originalUrl.includes('parent.masterSiteUrl') || originalUrl.includes('masterSiteUrl')) {
                        for (const [pattern, correctPath] of Object.entries(UPLOAD_INTERFACE_MAPPINGS)) {
                            if (originalUrl.includes(pattern)) {
                                const queryString = originalUrl.includes('?') ? originalUrl.substring(originalUrl.indexOf('?')) : '';
                                options.url = correctPath + queryString;
                                console.log(`✅ 修复AJAX上传URL: ${originalUrl} -> ${options.url}`);
                                fixCount++;
                                break;
                            }
                        }
                    } else {
                        // 修复其他路径
                        for (const [pattern, correctPath] of Object.entries(UPLOAD_INTERFACE_MAPPINGS)) {
                            if (originalUrl.includes(pattern) && originalUrl !== correctPath) {
                                const queryString = originalUrl.includes('?') ? originalUrl.substring(originalUrl.indexOf('?')) : '';
                                options.url = correctPath + queryString;
                                console.log(`✅ 修复AJAX上传URL: ${originalUrl} -> ${options.url}`);
                                fixCount++;
                                break;
                            }
                        }
                    }
                }
                return originalAjax.call(this, options);
            };
        }
        
        // 5. 修复页面中的JavaScript变量
        const scripts = document.querySelectorAll('script');
        scripts.forEach(script => {
            if (script.textContent) {
                let content = script.textContent;
                let modified = false;
                
                // 修复动态路径
                content = content.replace(/parent\.masterSiteUrl\s*\+\s*['"]upload\/file\.naii['"]/g, function(match) {
                    console.log(`✅ 修复脚本中的上传URL: ${match} -> '/upload/file.naii'`);
                    fixCount++;
                    modified = true;
                    return "'/upload/file.naii'";
                });
                
                content = content.replace(/masterSiteUrl\s*\+\s*['"]template\/uploadImage\.naii['"]/g, function(match) {
                    console.log(`✅ 修复脚本中的上传URL: ${match} -> '/template/uploadImage.naii'`);
                    fixCount++;
                    modified = true;
                    return "'/template/uploadImage.naii'";
                });
                
                if (modified) {
                    script.textContent = content;
                }
            }
        });
        
        console.log(`🎉 上传接口修复完成！共修复 ${fixCount} 处`);
        return fixCount;
    }
    
    // 检查接口可用性
    async function checkInterfaceAvailability() {
        console.log('🔍 检查上传接口可用性...');
        
        const results = {};
        const interfaces = Object.values(UPLOAD_INTERFACE_MAPPINGS);
        
        for (const interfaceUrl of interfaces) {
            try {
                const response = await fetch(interfaceUrl, { method: 'HEAD' });
                const status = response.status;
                
                if (status === 405) {
                    results[interfaceUrl] = '✅ 可用 (Method Not Allowed - 正常)';
                } else if (status === 404) {
                    results[interfaceUrl] = '❌ 不存在';
                } else if (status >= 200 && status < 300) {
                    results[interfaceUrl] = '✅ 可用';
                } else {
                    results[interfaceUrl] = `⚠️ 状态码: ${status}`;
                }
            } catch (error) {
                results[interfaceUrl] = '❌ 网络错误';
            }
        }
        
        console.table(results);
        return results;
    }
    
    // 生成修复报告
    function generateReport(fixCount, interfaceResults) {
        const report = {
            timestamp: new Date().toISOString(),
            fixCount: fixCount,
            interfaces: interfaceResults,
            summary: {
                totalInterfaces: Object.keys(interfaceResults).length,
                availableInterfaces: Object.values(interfaceResults).filter(status => status.includes('✅')).length,
                errorInterfaces: Object.values(interfaceResults).filter(status => status.includes('❌')).length
            }
        };
        
        console.log('📊 修复报告:', report);
        return report;
    }
    
    // 主执行函数
    async function main() {
        try {
            // 1. 修复上传接口
            const fixCount = fixUploadInterfaces();
            
            // 2. 检查接口可用性
            const interfaceResults = await checkInterfaceAvailability();
            
            // 3. 生成报告
            const report = generateReport(fixCount, interfaceResults);
            
            // 4. 将报告保存到sessionStorage
            sessionStorage.setItem('uploadInterfaceFixReport', JSON.stringify(report));
            
            console.log('🎉 上传接口统一修复完成！');
            console.log('📋 报告已保存到 sessionStorage.uploadInterfaceFixReport');
            
            return report;
        } catch (error) {
            console.error('❌ 修复过程中出现错误:', error);
            throw error;
        }
    }
    
    // 导出到全局
    window.UploadInterfaceFixer = {
        fix: fixUploadInterfaces,
        check: checkInterfaceAvailability,
        report: generateReport,
        run: main,
        mappings: UPLOAD_INTERFACE_MAPPINGS
    };
    
    // 自动执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', main);
    } else {
        main();
    }
    
})();
