# 上传接口统一映射文档

## 📋 接口分类和用途

### 1. **通用文件上传接口**
**控制器**: `UnifiedUploadController`  
**路径前缀**: `/upload`

| 接口路径 | 方法 | 用途 | 参数 | 前端调用位置 |
|---------|------|------|------|-------------|
| `/upload/file.naii` | POST | 通用文件上传 | file, type, size, exts | JSP表单上传 |
| `/upload/tinymce.naii` | POST | TinyMCE图片上传 | file | 富文本编辑器 |
| `/upload/markdown.naii` | POST | Markdown图片上传 | editormd-image-file | Markdown编辑器 |

### 2. **站点相关上传接口**
**控制器**: `manager.SiteController`  
**路径前缀**: `/sites`

| 接口路径 | 方法 | 用途 | 参数 | 前端调用位置 |
|---------|------|------|------|-------------|
| `/sites/uploadImage.naii` | POST | 站点图片上传 | image | 站点管理页面 |
| `/sites/tinymceUploadImage.naii` | POST | 富文本图片上传 | image | 富文本编辑器 |
| `/sites/uploadImageTinymce.naii` | POST | TinyMCE图片上传 | file, size, exts | TinyMCE编辑器 |
| `/sites/uploadImageTinymceFile.naii` | POST | TinyMCE文件上传 | file, accept, size, exts | TinyMCE编辑器 |

### 3. **模板相关上传接口**
**控制器**: `manager.TemplateController`  
**路径前缀**: `/template`

| 接口路径 | 方法 | 用途 | 参数 | 前端调用位置 |
|---------|------|------|------|-------------|
| `/template/uploadImage.naii` | POST | 模板图片上传 | image | 模板编辑页面 |

### 4. **插件相关上传接口**
**控制器**: `templateDevelop.PluginController`  
**路径前缀**: `/plugin/templateDevelop`

| 接口路径 | 方法 | 用途 | 参数 | 前端调用位置 |
|---------|------|------|------|-------------|
| `/plugin/templateDevelop/uploadPreview.naii` | POST | 预览图上传 | image | 模板开发插件 |

## 🎯 前端调用规范

### 1. **JSP文件中的调用**
```jsp
<!-- 通用文件上传 -->
<form action="/upload/file.naii" method="post" enctype="multipart/form-data">
    <input type="file" name="file">
    <input type="hidden" name="type" value="image">
</form>

<!-- 站点图片上传 -->
<form action="/sites/uploadImage.naii" method="post" enctype="multipart/form-data">
    <input type="file" name="image">
</form>
```

### 2. **JavaScript中的调用**
```javascript
// Layui上传组件
layui.upload.render({
    elem: '#uploadButton',
    url: '/upload/file.naii?type=image',
    field: 'file',
    accept: 'file',
    done: function(res) {
        // 处理上传结果
    }
});

// AJAX上传
$.ajax({
    url: '/sites/uploadImage.naii',
    type: 'POST',
    data: formData,
    processData: false,
    contentType: false,
    success: function(res) {
        // 处理上传结果
    }
});
```

### 3. **TinyMCE配置**
```javascript
tinymce.init({
    selector: 'textarea',
    images_upload_url: '/sites/uploadImageTinymceFile.naii',
    images_upload_handler: function(blobInfo, success, failure) {
        // 自定义上传处理
    }
});
```

## 🔧 接口参数说明

### 通用参数
- **file**: 上传的文件对象
- **type**: 文件类型 (image/file)
- **size**: 最大文件大小限制 (MB)
- **exts**: 允许的文件扩展名 (如: .jpg,.png,.gif)

### 特殊参数
- **accept**: TinyMCE接受的文件类型
- **image**: 专门用于图片上传的参数名
- **editormd-image-file**: Markdown编辑器专用参数名

## 📁 文件存储路径

### 1. **站点文件**
- 路径: `uploads/site/{siteId}/news/`
- 访问: `/site/{siteId}/news/{filename}`

### 2. **模板文件**
- 路径: `uploads/site/{siteId}/templateimage/`
- 访问: `/site/{siteId}/templateimage/{filename}`

### 3. **通用文件**
- 路径: `uploads/site/{siteId}/news/`
- 访问: `/uploads/site/{siteId}/news/{filename}`

## 🚫 已删除的重复接口

### admin包中的重复接口 (已删除)
- ~~`/sites/uploadImage.naii`~~ (admin.SiteController)
- ~~`/sites/tinymceUploadImage.naii`~~ (admin.SiteController)

**原因**: 与manager包的SiteController路径冲突，导致Spring无法正确路由。

## ✅ 接口测试

### 1. **测试工具**
- 测试页面: `/static/test-upload.html`
- 配置检查: `/test/static-config`

### 2. **测试步骤**
1. 启动应用
2. 访问测试页面
3. 测试各个上传接口
4. 验证文件是否正确保存和访问

### 3. **预期结果**
- 所有接口返回正确的JSON响应
- 上传的文件可以通过URL正常访问
- 不再出现404错误

## 🔄 版本兼容性

### 向后兼容
- 保留原有的接口路径
- 支持旧版本的参数格式
- 自动重定向到新的接口

### 前向兼容
- 统一的响应格式
- 标准化的错误处理
- 可扩展的参数结构

## 📝 维护说明

### 添加新的上传接口时
1. 确定接口的用途和分类
2. 选择合适的控制器和路径前缀
3. 更新此文档
4. 添加相应的测试用例

### 修改现有接口时
1. 保持向后兼容性
2. 更新文档说明
3. 通知前端开发人员
4. 更新测试用例
