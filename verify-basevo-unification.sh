#!/bin/bash

echo "🔍 BaseVO统一验证脚本"
echo "======================="

echo ""
echo "1. 检查是否还有tools.BaseVO的引用..."
TOOLS_BASEVO_COUNT=$(find . -name "*.java" -exec grep -l "cn\.edu\.sjtu\.gateway\.tools\.BaseVO" {} \; | wc -l)
echo "   发现 $TOOLS_BASEVO_COUNT 个文件引用tools.BaseVO"

if [ $TOOLS_BASEVO_COUNT -eq 0 ]; then
    echo "   ✅ 没有发现tools.BaseVO的引用"
else
    echo "   ❌ 仍有文件引用tools.BaseVO:"
    find . -name "*.java" -exec grep -l "cn\.edu\.sjtu\.gateway\.tools\.BaseVO" {} \;
fi

echo ""
echo "2. 检查vm.vo.BaseVO的使用情况..."
VM_BASEVO_COUNT=$(find . -name "*.java" -exec grep -l "cn\.edu\.sjtu\.gateway\.vm\.vo\.BaseVO" {} \; | wc -l)
echo "   发现 $VM_BASEVO_COUNT 个文件使用vm.vo.BaseVO"

echo ""
echo "3. 检查BaseVO文件是否存在..."
if [ -f "src/main/java/cn/edu/sjtu/gateway/tools/BaseVO.java" ]; then
    echo "   ❌ tools.BaseVO文件仍然存在"
else
    echo "   ✅ tools.BaseVO文件已删除"
fi

if [ -f "src/main/java/cn/edu/sjtu/gateway/vm/vo/BaseVO.java" ]; then
    echo "   ✅ vm.vo.BaseVO文件存在"
else
    echo "   ❌ vm.vo.BaseVO文件不存在"
fi

echo ""
echo "4. 编译验证..."
if mvn compile -q > /dev/null 2>&1; then
    echo "   ✅ 项目编译成功"
else
    echo "   ❌ 项目编译失败"
fi

echo ""
echo "5. 统计信息..."
echo "   - 继承BaseVO的类数量: $(find . -name "*.java" -exec grep -l "extends BaseVO" {} \; | wc -l)"
echo "   - 使用BaseVO的import数量: $(find . -name "*.java" -exec grep -l "import.*BaseVO" {} \; | wc -l)"

echo ""
echo "🎉 BaseVO统一验证完成！"
